<Table>
  <tr>
    <td><a href= "https://www.comgas.com.br/"><img src="img/comgas.png" alt="Comgas" border="0"></td>
    <td>
      <a href= "https://www.inteli.edu.br/"><img src="img/logo-Inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
    </td>
  </tr>
</table>

# Nome do Projeto: Carbon

## Nome do Grupo: Carbon

## Integrantes:

- <a href="https://www.linkedin.com/in/anacdejesus//">Ana Carolina de Jesus Pac<PERSON> da <PERSON></a>
- <a href="https://www.linkedin.com/in/felipe-zillo-72b367247/"><PERSON></a>
- <a href="https://www.linkedin.com/in/fernandobertholdo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/henrique-botti-6272571a0/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/karine-victoria//">Karine Victoria Rosa da Paixão</a>
- <a href="https://www.linkedin.com/in/natalycunha /">Nataly de Souza Cunha</a>
- <a href="https://www.linkedin.com/in/tainacortez/">Tainá de Paiva Cortez</a>

# Sumário

- [1. Introdução](#1-introdução)
  - [1.1 Termos e Abreviações](#11-termos-e-abreviações)
  - [1.2 Objetivo do Documento](#12-objetivo-do-documento)
- [2. Entendimento do Projeto e do Negócio](#2-entendimento-do-projeto-e-do-negócio)
  - [2.1 Contexto da Indústria do Parceiro](#21-contexto-da-indústria-do-parceiro)
  - [2.2 Problema](#22-problema)
  - [2.3 Visão do Projeto e do Produto](#23-visão-do-projeto-e-do-produto)
  - [2.4 Personas e Jornada do Usuário](#24-personas-e-jornada-do-usuário)
  - [2.5 Modelagem do Fluxo de Negócio](#25-modelagem-do-fluxo-de-negócio)
    - [2.5.1 Cadeia de Valor](#251-cadeia-de-valor)
    - [2.5.2 Fluxo de Negócio Proposto (AS-IS e TO-BE)](#252-fluxo-de-negócio-proposto-as-is-e-to-be)
  - [2.6 Matriz de Risco do Projeto](#26-matriz-de-risco-do-projeto)
  - [2.7 Ideação](#27-ideação)
  - [2.8 Canvas do Projeto](#28-canvas-do-projeto)
  - [2.9 Matriz SWOT](#29-matriz-swot)
- [3. Requisitos do Projeto](#3-requisitos-do-projeto)
  - [3.1 Business Drivers](#31-business-drivers)
  - [3.2 Requisitos Funcionais (RFs)](#32-requisitos-funcionais-rfs)
  - [3.3 Requisitos Não Funcionais (RNFs)](#33-requisitos-não-funcionais-rnfs)
  - [3.4 Correlação RFs e RNFs](#34-correlação-rfs-e-rnfs)
  - [3.5 Casos de Uso](#35-casos-de-uso)
  - [3.6 Visão inicial sobre a Solução Técnica de Engenharia](#36-visão-inicial-sobre-a-solução-técnica-de-engenharia)
- [4. Modelagem de Dados](#4-modelagem-de-dados)
  - [4.1 Especificação da Base de Dados para Modelo de Recomendação](#41-especificação-da-base-de-dados-para-modelo-de-recomendação)
  - [4.2 Modelo Conceitual de Dados](#42-modelo-conceitual-de-dados)
  - [4.3 Modelo Lógico de Dados](#43-modelo-lógico-de-dados)
  - [4.4 Modelo Físico de Dados](#44-modelo-físico-de-dados)
- [5. Solução Técnica (Design)](#5-solução-técnica-design)
  - [5.1 Diagrama de Classes](#51-diagrama-de-classes)
  - [5.2 Diagrama de Componentes da UML](#52-diagrama-de-componentes-da-uml)
  - [5.3 Diagrama de Sequência de Casos Críticos da UML](#53-diagrama-de-sequência-de-casos-críticos-da-uml)
  - [5.4 Visão Inicial sobre Tecnologias e Ferramentas](#54-visão-inicial-sobre-tecnologias-e-ferramentas)
  - [5.5 Escolha do algoritmo de Processamento de Linguagem Natural](#55-escolha-do-algoritmo-de-processamento-de-linguagem-natural)
  - [5.6 Pilha de Tecnologias para Implementação da Solução](#56-pilha-de-tecnologias-para-implementação-da-solução)
- [6. Mapeamento Técnico de Infraestrutura e Implantação](#6-mapeamento-técnico-de-infraestrutura-e-implantação)
  - [6.1 Diagrama de Implantação da UML](#61-diagrama-de-implantação-da-uml)
  - [6.2 Justificativa das Escolhas de Implantação](#62-justificativa-das-escolhas-de-implantação)
  - [6.3 Considerações sobre Desempenho e Segurança](#63-considerações-sobre-desempenho-e-segurança)
- [7. Projeto Visual da Solução](#7-projeto-visual-da-solução)
  - [7.1 Desenvolvimento de Wireframes](#71-desenvolvimento-de-wireframes)
  - [7.2 Desenvolvimento de Mockups](#72-desenvolvimento-de-mockups)
  - [7.3 Guia Visual](#73-guia-visual)
- [8. Desenvolvimento do Projeto](#8-desenvolvimento-do-projeto)
  - [8.1 Arquitetura de Codificação e Estrutura de Diretórios](#81-arquitetura-de-codificação-e-estrutura-de-diretórios)
  - [8.2 Modelo de Recomendação](#82-modelo-de-recomendação)
  - [8.3 Desenvolvimento de Features](#83-desenvolvimento-de-features)
    - [8.3.1 Sprint 3](#831-sprint-3)
    - [8.3.2 Sprint 4](#832-sprint-4)
    - [8.3.3 Sprint 5](#833-sprint-5)
  - [8.4 Testes Unitários e de Integração](#84-testes-unitários-e-de-integração)
  - [8.5 Documentações automáticas](#85-documentações-automáticas)
- [9. Planejamento e Execução de Testes](#9-planejamento-e-execução-de-testes)
  - [9.1 Testes Funcionais](#91-testes-funcionais)
    - [9.1.1 Planejamento](#911-planejamento)
    - [9.1.2 Resultados](#912-resultados)
  - [9.2 Testes de RNFs](#92-testes-de-rnfs)
    - [9.2.1 Planejamento](#921-planejamento)
    - [9.2.2 Resultados](#922-resultados)
  - [9.3 Testes de Usabilidade](#93-testes-de-usabilidade)
    - [9.3.1 Planejamento](#931-planejamento)
    - [9.3.2 Resultados](#932-resultados)
- [10. Procedimentos de Implantação](#10-procedimentos-de-implantação)
  - [10.1 Implantação e Configuração do Banco de Dados](#101-implantação-e-configuração-do-banco-de-dados)
  - [10.2 Implantação do Protótipo para uso por equipe de desenvolvimento](#102-implantação-do-protótipo-para-uso-por-equipe-de-desenvolvimento)
  - [10.3 Processo de Deploy do Algoritmo em Nuvem Comercial](#103-processo-de-deploy-do-algoritmo-em-nuvem-comercial)
- [Referências](#referências)

# 1. Introdução

&emsp;No presente documento, são documentados os detalhes de entendimento da empresa parceira, a Comgás, e é detalhado o processo de desenvolvimento de um sistema de automação com reconhecimento de áudio e interações em linguagem natural (LN), empregando tecnologias como reconhecimento de voz, processamento de linguagem natural (PLN), como maneira de mitigar a problemática referente à baixa abrangência na analise e auditoria do atendimento ao cliente.

## 1.1 Termos e Abreviações

| Termo / Sigla | Definição |
|---------------|-----------|
| **ARSESP** | Agência Reguladora de Serviços Públicos do Estado de São Paulo, responsável por regular e fiscalizar o serviço de distribuição de gás canalizado no estado. |
| **BPMN** | *Business Process Model and Notation* — notação padrão para modelagem de processos de negócio. |
| **CX** | *Customer Experience* — experiência do cliente ao interagir com a empresa. |
| **FCR** | *First Contact Resolution* — indicador de atendimento que mede a porcentagem de casos resolvidos no primeiro contato do cliente. |
| **IA** | Inteligência Artificial — campo da ciência da computação que desenvolve sistemas capazes de executar tarefas que normalmente requerem inteligência humana. |
| **MVP** | *Minimum Viable Product* — produto mínimo viável; versão inicial de um produto com funcionalidades essenciais para validação. |
| **PLN** | Processamento de Linguagem Natural — área da IA que trata da interação entre computadores e linguagem humana. |
| **PNL** | Processamento de Linguagem Natural (sinônimo de PLN). |
| **QA** | *Quality Assurance* — garantia da qualidade; conjunto de atividades para assegurar que um produto ou serviço atenda aos requisitos de qualidade. |
| **RNF** | Requisito Não Funcional — especificação de características ou restrições de um sistema, como desempenho, segurança ou usabilidade. |
| **RF** | Requisito Funcional — especificação de funções ou comportamentos que um sistema deve executar. |
| **SAC** | Serviço de Atendimento ao Consumidor — canal regulamentado para atendimento ao cliente. |
| **Speech Analytics** | Tecnologia que aplica reconhecimento de voz e análise para extrair informações e insights de conversas gravadas. |
| **TO-BE** | Representação de como um processo será no futuro, após melhorias ou mudanças. |
| **AS-IS** | Representação de como um processo funciona atualmente, antes de melhorias ou mudanças. |
| **Dashboard** | Painel visual para monitoramento e análise de métricas e indicadores de desempenho. |
| **API** | *Application Programming Interface* — conjunto de rotinas e padrões para integração entre sistemas. |


# 2. Entendimento do Projeto e do Negócio

&emsp;Nesta seção, será apresentado o entendimento completo do escopo do projeto, detalhando a problemática enfrentada e o papel estratégico da Comgás no setor de distribuição de gás natural.

## 2.1 Contexto da Indústria do Parceiro

### Visão Geral do Setor

&emsp; O mercado brasileiro de gás natural atravessa uma transformação estrutural significativa, impulsionada pela Nova Lei do Gás (Lei 14.134/2021) que busca promover a concorrência e reduzir os preços do energético. Atualmente, 85% da produção de gás natural no país é offshore, sendo 84% do pré-sal, demonstrando a concentração geográfica da produção nacional.

&emsp; O setor caracteriza-se por um ambiente regulatório complexo, com competências divididas entre União e Estados. A regulamentação da Nova Lei do Gás foi feita pelo Decreto nº 10.712, de 2 de junho de 2021, estabelecendo diretrizes federais, enquanto no Brasil, a distribuição de gás canalizado é um monopólio Estadual, e cada ente da federação outorga essa atividade a uma concessionária.

### Principais Tendências e Desafios

**1. Abertura do Mercado Livre**
&emsp; O mercado de gás natural brasileiro está em processo de abertura gradual, similar ao que ocorreu no setor elétrico. A Nova Lei do Gás (14.134/2021) tem como principal objetivo a promoção da concorrência do mercado de gás natural, favorecendo a maior competitividade do preço do energético.

**2. Digitalização e Automação**
&emsp; No caso das utilities (energia, água, e gás, basicamente), as organizações do setor procuram formas de modernizar suas atividades e atender melhor ao seu consumidor. A transformação digital inclui desenvolvimento de aplicativos, automação de processos e uso estruturado de dados para melhorar a experiência do cliente.

**3. Transição Energética**
&emsp; O setor enfrenta pressões crescentes para a descarbonização, com o biometano emergindo como alternativa renovável. O gás natural é o combustível fóssil menos poluente de todos, posicionando-se como combustível de transição.

**4. Infraestrutura Limitada**
&emsp; Apenas 5% dos municípios do país tem acesso a rede de gás canalizado, evidenciando o enorme potencial para desenvolvimento da malha de distribuição, representando uma oportunidade e desafio para expansão.

### Posicionamento do Parceiro

#### Liderança de Mercado

&emsp; A Comgás ocupa posição dominante como maior distribuidora de gás natural canalizado da América Latina, com dados operacionais expressivos:
- **2,6 milhões de clientes** atendidos
- **97 municípios** de presença no estado de São Paulo
- **450 novos clientes conectados diariamente**
- **23.000 quilômetros de rede** de distribuição
- **27% do PIB nacional** na região de concessão
- **30% do gás distribuído no Brasil** em sua área de atuação

#### Área de Concessão Estratégica

&emsp; A empresa atua nas regiões mais desenvolvidas do estado de São Paulo: Região Metropolitana de São Paulo, Região Metropolitana de Campinas, Baixada Santista e Vale do Paraíba. A concessão vigora até 2049, proporcionando horizonte de planejamento de longo prazo.

#### Modelo Regulatório Diferenciado

&emsp; A Comgás opera sob regulação da ARSESP (Agência Reguladora de Serviços Públicos do Estado de São Paulo), reconhecida por suas boas práticas. É inequívoco que a atuação técnica, independente da Arsesp é um diferencial decisivo no sucesso desse modelo. São Paulo possui modelo regulatório de sucesso no setor de gás natural, com regulação do mercado livre estabelecida desde 2011.

### Estrutura Corporativa

&emsp; A Comgás integra o Grupo Compass, controlado pela Cosan, que também possui participações em outras distribuidoras brasileiras. Em três anos de história, já investiram quase R$ 9 bilhões no mercado brasileiro de gás natural.

#### Inovação e Digitalização

&emsp; A empresa está alinhada às tendências do setor, com 94% das interações dos clientes por meio digital, evidenciando sua estratégia de automação e eficiência operacional. Possui centro de inovação (Plugue) e investe em soluções tecnológicas, incluindo:
- Assistente virtual (Cris) com taxa de resolução de 59-85% dependendo do canal
- Múltiplos canais digitais (WhatsApp, chat (app e site))
- Programas de P&D e parcerias com startups
- Investimentos em biometano e outras energias renováveis

#### Posicionamento Competitivo

&emsp; Em um mercado caracterizado por regulação estadual e concessões territoriais, a Comgás se destaca pela:
- **Tradição centenária** (153 anos de história)
- **Base de clientes consolidada** e em crescimento
- **Infraestrutura robusta** na região mais rica do país
- **Cultura de inovação** e transformação digital
- **Compromisso com sustentabilidade** e transição energética

&emsp; Este contexto posiciona a empresa para liderar a modernização do atendimento ao cliente no setor de utilities brasileiro, justificando a relevância do projeto de automação com reconhecimento de áudio proposto.

## 2.2 Problema

&emsp;O projeto da Comgás enfrenta um desafio clássico de operações de atendimento terceirizadas: medir, com precisão e escala, a qualidade das interações telefônicas para elevar a eficácia de resolução dos casos do cliente (especialmente First Contact Resolution). Na prática, a baixa cobertura de QA — com monitoria manual tipicamente limitada a 1–3% das ligações — gera pouca visibilidade sobre o que ocorre em cada contato e dificulta a gestão por desempenho do fornecedor terceirizado. Por consequência, insights críticos (aderência a roteiro, prazos, causas de retrabalho) passam despercebidos. A adoção de speech analytics/QM automatizado permite ampliar a análise para próximo de 100% das chamadas, criando base comparável e auditável para intervenções de melhoria e coaching.

&emsp;No Brasil, a governança desse processo também é regulatória: o Decreto nº 11.034/2022 (Lei do SAC) estabelece requisitos de acesso, prazos de resposta e transparência, e as distribuidoras paulistas são acompanhadas pela ARSESP por indicadores de atendimento — o que exige métricas formais e trilhas de auditoria também no canal telefônico.

&emsp;Impacto 1 — Qualidade como alavanca de melhoria contínua. A qualidade do atendimento é fonte direta de insights operacionais. Evidência setorial indica que cada contato adicional necessário para resolver um caso reduz a satisfação média em ~16%, reforçando a importância de elevar o First Contact Resolution (FCR) e reduzir recontatos.

&emsp;Impacto 2 — Gestão objetiva do terceirizado e conformidade. Sem análise abrangente das ligações, a empresa não consegue medir desempenho, satisfazer obrigações do Decreto 11.034/2022 nem comprovar aderência a indicadores regulatórios (ex.: prazos e qualidade do atendimento acompanhados pela ARSESP). Automação analítica supre essa lacuna ao gerar painéis rastreáveis e comparáveis por parceiro, equipe e motivo de contato.

## 2.3 Visão do Projeto e do Produto

### 2.3.1 Objetivos do produto

&emsp; O produto tem como principal objetivo automatizar a avaliação da qualidade dos atendimentos realizados por telefone, entre clientes e equipes terceirizadas da Comgás. Utilizando técnicas de processamento de linguagem natural (PLN), ele é capaz de interpretar o conteúdo das ligações e verificar se os atendentes seguiram corretamente os critérios definidos pela empresa — como o uso adequado do script, clareza na comunicação e orientações específicas conforme o tipo de chamado.

&emsp; Com isso, a ferramenta busca aumentar a eficiência do processo de análise, que hoje é feito de forma manual e cobre apenas uma pequena parte das ligações (1%). Ao automatizar essa tarefa, é possível avaliar um volume muito maior de atendimentos, sem aumento de custo, e com mais agilidade na identificação de falhas ou oportunidades de melhoria.

&emsp; Além de apoiar a equipe de Qualidade no monitoramento das ligações, o sistema também ajuda a gerar mais consciência dentro do time de atendimento, ao apontar com mais clareza os comportamentos que precisam ser reforçados ou ajustados. 

&emsp;Mais do que uma ferramenta de análise, o produto se propõe a ser um apoio na tomada de decisões e na melhoria contínua da experiência do cliente.

---

### 2.3.2 O que o produto faz / não faz

**O que o produto faz**

- **Análise automatizada de ligações:** Aplica algoritmos de PLN para interpretar o conteúdo das chamadas e avaliar sua qualidade com base em critérios pré-definidos pela Comgás.
- **Verificação de aderência a scripts:** Identifica se o atendente seguiu as orientações corretas para cada tipo de atendimento (ex: em casos de vazamento, orientar o cliente a abrir janelas).
- **Cálculo de nota de atendimento:** Atribui uma pontuação a cada chamada com base em atributos como cordialidade, resolutividade e tempo de resposta.
- **Dashboard de visualização:** Apresenta os resultados da análise em uma interface clara para que a equipe de Qualidade possa acompanhar os dados e tomar decisões.
- **Geração de insights para melhoria:** Indica padrões recorrentes nos atendimentos, apontando falhas comuns e comportamentos desejados que podem ser reforçados.

**O que o produto não faz**

- **Monitoramento em tempo real:** A análise é feita após o término da ligação, não durante a chamada.
- **Predição de comportamento futuro:** O modelo não prevê resultados ou riscos futuros com base nos dados.
- **Resposta automatizada ao cliente:** O sistema não interage diretamente com o consumidor final nem substitui o atendente.
- **Análise de outros canais:** A versão inicial do produto foca exclusivamente em chamadas telefônicas, sem incluir canais digitais como e-mail ou WhatsApp.
- **Substituição completa da análise humana:** A ferramenta atua como apoio à equipe de Qualidade, mas não elimina a necessidade de revisões manuais em casos específicos ou críticos.

## 2.4 Personas e Jornada do Usuário

&emsp; As personas de Marcela e Felipe, juntamente com seus respectivos mapas de jornada, são a base do nosso projeto. Elas traduzem as necessidades e frustrações das equipes da Comgás em um formato compreensível e acionável para o time de desenvolvimento.

### Marcela - Analista de Qualidade e Canais Digitais

&emsp; Marcela representa a **equipe de canais digitais**, responsável por toda a parte de atendimento automatizado da Comgás. Para essa equipe, o projeto não é apenas sobre auditoria de ligações, mas também sobre fornecer material para a otimização do autoatendimento.

- **Desafio Principal**: Tomar decisões sobre melhorias no chatbot e autoatendimento "no escuro", sem dados concretos sobre o que faz os clientes migrarem do digital para o atendimento humano.

- **Valor da Solução**: Nossa solução preenche esse "ponto cego", permitindo que a equipe use dados concretos de voz para aprimorar o autoatendimento, justificar suas ações e provar o valor de seu trabalho.

<div align="center">
<sub>Figura X - Persona Marcela</sub>
  <img src="../documentos/img/personas_jornadas/Persona-Marcela.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

### Felipe - Analista de Relacionamento e Atendimento ao Cliente

&emsp; Felipe representa a **equipe de relacionamento com cliente**, responsável por toda a parte de atendimento humano e gestão da central terceirizada. Para essa equipe, o projeto é sobre eficiência operacional e credibilidade técnica.

- **Desafio Principal**: Auditoria manual de apenas 1% das ligações consome tempo excessivo e gera dados não confiáveis, minando sua credibilidade com gestores e parceiros terceirizados.

- **Valor da Solução**: A solução transforma Felipe de um auditor manual em um analista estratégico, fornecendo amostragem inteligente e confiável que permite uso mais produtivo do tempo e confiança para tomar decisões e exigir melhorias.

<div align="center">
<sub>Figura X - Persona Felipe</sub>
  <img src="../documentos/img/personas_jornadas/Persona-Felipe.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

### Jornadas de Usuário

&emsp; As jornadas de usuário são apresentadas em dois momentos distintos: **"Antes da Solução"** e **"Depois da Solução"**. Essa abordagem permite visualizar claramente o contraste entre a situação atual problemática e o cenário ideal após a implementação da nossa proposta, demonstrando o valor concreto que a solução entregará para cada equipe.

#### Jornada de Marcela - Equipe de Canais Digitais

&emsp; A jornada de Marcela mostra a evolução de decisões tomadas "no escuro", sem dados de voz para justificar melhorias no autoatendimento, para uma abordagem data-driven que permite identificar problemas reais, propor soluções embasadas e comprovar o impacto de seu trabalho.

<div align="center">
<sub>Figura X - Jornada de Usuário - Marcela - Antes da Solução</sub>
  <img src="../documentos/img/personas_jornadas/Jornada-Marcela-Antes.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura X - Jornada de Usuário - Marcela - Depois da Solução</sub>
  <img src="../documentos/img/personas_jornadas/Jornada-Marcela-Depois.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

#### Jornada de Felipe - Equipe de Atendimento Humano

&emsp; A jornada de Felipe ilustra a transformação de uma rotina exaustiva e ineficaz, onde a auditoria manual consome tempo e gera dados não confiáveis, para uma rotina produtiva e estratégica, onde a automação libera seu tempo para análises mais profundas e aumenta sua credibilidade.

<div align="center">
<sub>Figura X - Jornada de Usuário - Felipe - Antes da Solução</sub>
  <img src="../documentos/img/personas_jornadas/Jornada-Felipe-Antes.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura X - Jornada de Usuário - Felipe - Depois da Solução</sub>
  <img src="../documentos/img/personas_jornadas/Jornada-Felipe-Depois.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>


## 2.5 Modelagem do Fluxo de Negócio

&emsp;Nesta seção, serão detalhados os fluxos de negócio atuais da organização, bem como apresentadas sugestões de melhorias. A compreensão aprofundada desses fluxos é fundamental para garantir que a solução proposta atenda de forma eficaz às necessidades do negócio, permitindo identificar pontos de otimização e possíveis gargalos nos processos existentes.

## 2.5.1 Cadeia de Valor


&emsp;A cadeia de valor do projeto **Automação de Monitoramento de Qualidade de Ligações (Comgás – Projeto M7 TAPI)** foi modelada no formato Porter, com base em informações coletadas durante a Sprint 1 e nas definições de produto e funcionalidades priorizadas no planejamento do módulo. O objetivo desta representação é demonstrar, de forma estruturada, como as principais atividades de suporte e atividades primárias se relacionam no desenvolvimento e operação da solução proposta.

&emsp;A estrutura adota o formato clássico com **Atividades de Suporte** (Infraestrutura da Empresa, Gestão de Recursos Humanos, Desenvolvimento Tecnológico e Aquisição/Procurement) e **Atividades Primárias** (Logística de Entrada, Operações, Logística de Saída, Marketing e Vendas e Serviço), finalizando com a margem (*Margin*) que sintetiza o ganho de valor obtido com a solução.

<div align="center">
<sub>Cadeia de Valor – Automação de Monitoramento de Qualidade de Ligações</sub>
  <img src="../documentos/img/cadeia_valor.png" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

### **Atividades de Suporte**

**Infraestrutura da Empresa**  
&emsp;Inclui a governança de dados e conformidade com a LGPD, gestão integrada entre as áreas de Experiência do Cliente (CX) e Tecnologia da Informação (TI) e a coordenação técnica do projeto. Este suporte garante que a operação do sistema e a evolução das entregas ocorram em conformidade com padrões de qualidade e requisitos regulatórios.

**Gestão de Recursos Humanos**  
&emsp;Abrange a capacitação avançada da equipe envolvida no uso do sistema, treinamentos aprofundados sobre critérios de qualidade e atendimento e a manutenção de uma cultura organizacional centrada no cliente, voltada para a melhoria contínua.

**Desenvolvimento Tecnológico**  
&emsp;Compreende a concepção e evolução dos modelos de Processamento de Linguagem Natural (PLN) para análise de cordialidade e aderência a scripts, a utilização de serviços robustos de transcrição automática (como Whisper, Google e AWS) e a implementação de dashboards interativos que facilitem a análise de KPIs.

**Aquisição / Procurement**  
&emsp;Envolve a contratação de serviços de nuvem confiáveis, licenças para APIs Speech-to-Text multilíngues e aquisição de ferramentas para análise e armazenamento de dados em larga escala, assegurando infraestrutura tecnológica estável e segura.

---

### **Atividades Primárias**

**Logística de Entrada**  
&emsp;Inclui o recebimento de ligações gravadas (simuladas ou reais), a padronização e conversão dos formatos de áudio para processamento e a anonimização rigorosa de dados sensíveis, garantindo privacidade e segurança.

**Operações**  
&emsp;Abrange o processamento do áudio para texto, a análise de cordialidade, aderência a script e tempo de fala, bem como a geração de uma pontuação (score) padronizada de 0 a 5 para cada ligação.

**Logística de Saída**  
&emsp;Compreende o armazenamento estruturado dos resultados, sua disponibilização em dashboards e a exportação de relatórios para consumo das equipes de qualidade e gestão.

**Marketing e Vendas Internas**  
&emsp;Refere-se à apresentação das métricas e resultados para os gestores, divulgação de padrões e insights e ao engajamento das equipes no processo de melhoria da qualidade.

**Serviço**  
&emsp;Inclui a coleta de feedback de analistas, ajustes e revalidações nos critérios de avaliação e o treinamento contínuo dos modelos de IA, garantindo a evolução da solução.

---

### Observações sobre a elaboração do diagrama

&emsp;O diagrama de Cadeia de Valor foi produzido com caráter **acadêmico e de aprendizado**, no contexto do projeto do Módulo 7 do curso de Engenharia de Computação do Inteli, em parceria com a Comgás.  
Apesar de se inspirar em práticas comuns de cadeia de valor, parte do conteúdo – especialmente nas atividades de suporte – foi elaborado com base em **presunções derivadas do escopo do projeto, dos objetivos do produto e do brainstorming de funcionalidades** realizados pela equipe, e **não reflete necessariamente a estrutura ou operação real da Comgás**.

---

### Fontes utilizadas

- **Documento TAPI – Projeto PLN de Automação com Reconhecimento de Áudio e Texto**: informações sobre escopo, objetivos, contexto do parceiro e critérios de sucesso.  
- **Visão do Produto e Brainstorming de Features** (exercício acadêmico): definição de funcionalidades, priorização e critérios de impacto.  
- **Encontros de kick-off e workshop com o parceiro na Sprint 1**: alinhamento inicial de expectativas, compreensão do problema e do fluxo de trabalho atual.  
- **Reuniões internas da equipe**: consolidação das ideias para modelagem da cadeia de valor e detalhamento de atividades de suporte e primárias.

## 2.5.2 Fluxo de Negócio Proposto (AS-IS e TO-BE)

&emsp;O mapeamento dos processos de negócio (BPMN) apresenta a transformação do processo de análise de qualidade de chamadas, desde o modelo atual manual até a solução automatizada proposta. O objetivo é desenvolver um sistema baseado em Processamento de Linguagem Natural (PNL) para avaliar interações entre atendentes e clientes.

&emsp;Atualmente, o processo é manual e reativo, com cobertura de apenas 1% das chamadas. A solução proposta visa aumentar drasticamente essa cobertura, automatizando tarefas repetitivas e elevando a equipe de uma função operacional para estratégica.

### Processo Atual (AS-IS)

&emsp;O processo AS-IS descreve o fluxo de trabalho atual: um sistema manual, iniciado por demanda e com baixa escala.

**Diagrama do Processo AS-IS:**

&emsp;O diagrama ilustra o fluxo macro do processo atual, mostrando a interação entre os participantes e o ciclo de análise. A complexidade da avaliação de cada chamada foi abstraída em um subprocesso para maior clareza.


<div align="center">
<sub>Figura X - Fluxo de Negócios BPMN - as is</sub>
  <img src="../documentos/img/fluxo-negocios/asis.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>


<div align="center">
<sub>Figura X - Fluxo de Negócios BPMN - subprocesso do as is</sub>
  <img src="../documentos/img/fluxo-negocios/subprocesso.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>


**Detalhamento do Fluxo:**

&emsp;O processo é executado por dois papéis principais: **Analista de Qualidade** e **Superior**, com participação de um **Fornecedor de Gravações** externo.

1. **Início por Demanda:** O processo é iniciado quando o Analista recebe uma solicitação formal de seu Superior.
2. **Obtenção de Insumos:** O Analista solicita o lote de gravações a ser analisado para o Fornecedor Terceirizado e aguarda o recebimento.
3. **Ciclo de Análise Manual:** O Analista entra em um ciclo para analisar uma quantidade pré-definida de chamadas, executando análise detalhada para cada uma.
    - Para cada chamada, ele executa um Subprocesso de análise detalhada, que consiste em ouvir a gravação e pontuar múltiplos critérios de qualidade (conforme detalhado no diagrama de Nível 2).

      &emsp;Para garantir a clareza e legibilidade do diagrama principal, a sequência de tarefas detalhadas para a avaliação de uma única chamada foi encapsulada em um Subprocesso. Este elemento, identificado no diagrama pelo símbolo [+], funciona como um contêiner para um fluxo de trabalho secundário e mais detalhado (Nível 2), onde cada critério de qualidade (tom de voz, cumprimento de roteiro, etc.) é verificado individualmente. 

4. **Consolidação e Relatório:** Após atingir a cota, o Analista consolida as avaliações manuais e elabora um relatório.
5. **Finalização:** O fluxo é finalizado após o envio e revisão do relatório pelo Superior.

**Principais Limitações:**

- **Baixa Cobertura:** A natureza manual permite analisar apenas ~1% do total de chamadas, resultando em visão limitada da qualidade geral
- **Processo Reativo:** O trabalho só começa após solicitação, impedindo identificação proativa de problemas
- **Alto Custo Operacional:** Tempo dos analistas consumido por tarefas repetitivas de baixo valor agregado
- **Insights Limitados:** Relatórios baseados em amostras pequenas, dificultando identificação de tendências confiáveis

### Processo Futuro (TO-BE)

&emsp;O processo TO-BE foi projetado para ser proativo, escalável e estratégico, com tecnologia executando tarefas e analistas atuando como agentes de inteligência.

**Diagrama do Processo TO-BE:**

&emsp;O diagrama ilustra o novo fluxo de trabalho, com a introdução do **Sistema de Análise (IA)** como participante ativo no processo.

<div align="center">
<sub>Figura X - Fluxo de Negócios BPMN - to be</sub>
  <img src="../documentos/img/fluxo-negocios/tobe.svg" width="100%">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>


**Detalhamento do Fluxo:**

&emsp;O novo processo reorganiza as responsabilidades e automatiza a maior parte do fluxo de trabalho.

1. **Início Proativo e Automático:** O processo é iniciado automaticamente em ciclos pré-definidos (ex: diariamente), sem necessidade de intervenção humana.
2. **Execução da IA:** O **Sistema de Análise (IA)** realiza automaticamente:
   - Coleta de lote amplo e relevante de gravações
   - Processamento e avaliação usando modelo de PNL
   - Consolidação de dados e atualização de dashboard com métricas
3. **Análise Humana Estratégica:** O **Analista de Qualidade** analisa o dashboard para identificar tendências, exceções e padrões.
4. **Fluxo de Decisão:** Com base na análise, segue um de dois caminhos:
   - **Monitoramento:** Se nenhum ponto crítico for identificado, o ciclo se encerra
   - **Ação:** Se necessário, o Analista aprofunda a análise e elabora relatório com insights estratégicos
5. **Finalização com Valor:** O relatório é enviado ao **Superior** para definição de plano de ação.

**Benefícios Esperados:**

- **Cobertura Ampla:** Análise de amostragem estatisticamente relevante, fornecendo visão fiel da qualidade
- **Proatividade:** Análise contínua permite identificar problemas em tempo real
- **Redução de Custo:** Libera tempo do analista para atividades de maior valor
- **Foco Estratégico:** Evolução do papel do analista de coletor para intérprete de insights
- **Decisões Baseadas em Dados:** Ações fundamentadas em análise completa, não apenas amostras pequenas

### Análise Comparativa

&emsp;A tabela abaixo resume a transformação proposta pelo projeto:

| **Atributo**          | **Processo AS-IS (Manual)**           | **Processo TO-BE (Com IA)**                |
| :-------------------- | :------------------------------------ | :----------------------------------------- |
| **Natureza**          | Reativo e sob demanda                 | Proativo e contínuo (agendado)             |
| **Cobertura**         | Amostral (~1%)                        | Amostragem Ampla e Relevante               |
| **Papel do Analista** | Operacional: Ouve, preenche, compila  | Estratégico: Analisa, investiga, recomenda |
| **Output Final**      | Relatório de dados brutos             | Plano de Ação baseado em insights          |
| **Gargalo**           | Tempo humano para ouvir e classificar | Capacidade humana para analisar insights   |

&emsp;O projeto não apenas otimiza um processo existente, mas o **redefine fundamentalmente**. A área de Análise de Qualidade evolui de um centro de auditoria operacional para um núcleo de inteligência de negócio, gerando valor estratégico contínuo para a organização.

## 2.6 Matriz de Risco do Projeto

### 2.6.1 Matriz de Riscos do Produto/Projeto

A matriz de riscos e oportunidades é uma ferramenta que ajuda a visualizar, de forma simples, quais eventos podem impactar o projeto, avaliando a probabilidade e o impacto de cada um. Ela também aponta pontos positivos que podem ser aproveitados como oportunidades.

<div align="center">
  <sub>Figura X - Matriz de Riscos e Oportunidades </sub> <br>

  <img src="img/matriz-risco-produto.png" alt="Matriz de Riscos e Oportunidades" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp; No quadro a seguir, você encontra a descrição de cada risco apresentado na imagem acima, junto com o plano de ação para cada um deles.

<div align="center">
<sub> Quadro X  </sub>

| **Nº** | **Risco** | **Descrição** | **Plano de Ação** |
|--------|-----------|---------------|-------------------|
| 1 | **Simulações não refletirem casos reais** | Os áudios simulados das transcrições não refletirem a variabilidade real dos atendimentos (ex.: tipos de clientes, problemas, tons emocionais), levando a um modelo que não avalia corretamente a qualidade do atendimento.  | - Fazer simulações que sejam o mais fiéis possível às transcrições reais.<br>- Incluir cenários diversos (ex.: reclamações, elogios, dúvidas técnicas). |
| 2 | **Baixa acurácia no processamento de linguagem natural e transcrição** | Erros no reconhecimento de voz (ex.: falhas em transcrições) ou na análise de intenções e sentimentos (ex.: confundir ironia com satisfação) resultam em scores imprecisos, comprometendo a confiabilidade do modelo. | - Utilizar APIs robustas de speech-to-text (ex.: Google Cloud Speech-to-Text) e modelos de PLN pré-treinados.<br> - Validar resultados com avaliações humanas em amostras iniciais.<br>- Implementar regras baseadas em palavras-chave para complementar a análise de sentimento. |
| 3 | **Falhas no reconhecimento de voz devido a sotaques e ruídos** | O modelo de speech-to-text pode falhar ao processar áudios com sotaques regionais ou ruídos de fundo, gerando transcrições imprecisas que afetam a análise de qualidade. | - Escolher APIs de speech-to-text com suporte a português brasileiro e redução de ruídos.<br>- Simular áudios com sotaques e ruídos para calibrar o modelo. |
| 4 | **Dificuldade na definição de critérios objetivos de qualidade** | Critérios como cordialidade ou cumprimento de scripts podem ser subjetivos, dificultando a criação de métricas consistentes e levando a avaliações incoerentes. | - Definir critérios claros e mensuráveis em conjunto com os líderes de negócio (ex.: presença de saudações, tempo de fala menor que 70%). |
| 5 | **Falta de generalização do modelo** | O modelo pode não performar bem em novos cenários de atendimento (ex.: reclamações não vistas no treinamento), reduzindo sua escalabilidade. | - Garantir diversidade nos dados de treinamento (ex.: diferentes tipos de chamadas). |
| 6 | **Interface de visualização confusa ou pouco intuitiva** | O dashboard pode ser difícil de usar ou não atender às necessidades dos analistas, dificultando a interpretação dos scores e a adoção da solução. | - Prototipar o dashboard com ferramentas simples e testar com stakeholders.<br>- Iterar o design com base em feedback de Karina, Bruno e Gizele. |
| 7 | **Dependência de serviços de terceiros** | Falhas ou limitações em APIs de speech-to-text/text-to-speech (ex.: indisponibilidade, cotas excedidas) podem interromper o funcionamento do protótipo. | - Escolher provedores confiáveis (ex.: AWS, Google) com SLAs robustos.<br>- Monitorar uso de APIs para evitar exceder cotas. |

<sup>Fonte: Material produzido pelos autores (2025)</sup>

</div>

&emsp; A imagem acima também destaca, no lado direito, as oportunidades que envolvem o projeto. A seguir tem-se uma descrição de cada uma dessas oportunidades.

<div align="center">
<sub> Quadro X  </sub>

| **Nº** | **Oportunidades** | **Descrição** |
|--------|-------------------|---------------|
| 1 | **Melhoria na identificação de padrões de atendimento** | A análise automatizada de grandes volumes de chamadas pode revelar padrões (ex.: problemas recorrentes, falhas em scripts, comportamentos dos atendentes), permitindo ações corretivas mais rápidas e precisas. |
| 2 | **Redução de custos operacionais** | Substituir a análise manual por um sistema automatizado pode reduzir o esforço humano, permitindo realocar analistas para tarefas estratégicas, como treinamento ou resolução de casos complexos. |
| 3 | **Integração com canais digitais** | A solução pode ser adaptada para monitorar interações em canais digitais (ex.: WhatsApp, chatbot), alinhando-se à estratégia da Comgás de priorizar o atendimento digital. |

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>


### 2.6.2 Matriz de Riscos da Equipe

&emsp; A matriz a seguir representa os principais riscos identificados na **equipe do projeto**, levando em consideração fatores internos como prazos, comunicação, alinhamento de escopo e adaptação tecnológica. 

<div align="center">
  <sub>Figura X - Matriz de Riscos e Oportunidades</sub> <br>

  <img src="img/matriz-risco-equipe.png" alt="Matriz de Riscos e Oportunidades" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>

&emsp; No quadro a seguir, você encontra a descrição de cada risco apresentado na imagem acima, junto com o plano de ação para mitigá-los.

<div align="center">
<sub>Quadro X - Riscos da Equipe</sub>

| **Nº** | **Risco** | **Descrição** | **Plano de Ação** |
|-------|-----------|---------------|-------------------|
| **1** | Descomprometimento com prazos estabelecidos | Atrasos na entrega de tarefas, como desenvolvimento do modelo de PLN, simulação de áudios ou criação do dashboard, podem comprometer o cronograma do protótipo. | - Seguir o planejamento da Planning.<br>- Usar ferramentas de gestão para acompanhar o progresso, como fazer bom uso do Trello. |
| **2** | Comunicação ineficaz com stakeholders | Informações mal interpretadas ou falta de alinhamento com stakeholders (ex.: Karina, Bruno, Gizele) podem levar a entregas desalinhadas com as expectativas da Comgás. | - Apresentar sempre o progresso e ideias nas sprint reviews para validação. |
| **5** | Dificuldade na adaptação a novas ferramentas | A curva de aprendizado para APIs de speech-to-text (ex.: Google Cloud) ou modelos de PLN pode reduzir a produtividade da equipe no desenvolvimento do protótipo. | - Incentivar o compartilhamento de conhecimento entre os membros. |
| **4** | Escopo mal definido ou mudanças frequentes | Alterações nos requisitos (ex.: novos critérios de qualidade, mudanças no dashboard) podem causar retrabalho e atrasos no desenvolvimento do protótipo. | - Documentar o escopo do MVP detalhadamente com os stakeholders no início.<br>- Focar nas funcionalidades mínimas viáveis. |
| **3** | Divergências na visão do projeto entre a equipe | Diferentes interpretações do escopo podem gerar retrabalho ou desalinhamento com os objetivos da Comgás. | - Realizar reuniões iniciais de alinhamento com todos os membros.<br>- Revisar o progresso semanalmente para garantir consenso. |
| **6** | Comunicação ineficaz da equipe | Informações podem ser perdidas ou mal interpretadas dentro da equipe, afetando entregas. | - Estabelecer canais de comunicação claros e reuniões regulares para alinhamento. |

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp; A imagem acima também destaca, no lado direito, as oportunidades que envolvem o projeto. A seguir, apresenta-se uma descrição de cada uma dessas oportunidades.

<div align="center">
<sub>Quadro X - Oportunidades da Equipe</sub>

| **Nº** | **Oportunidade** | **Descrição** |
|-------|-----------------|---------------|
| 1     | Aprimorar habilidades em tecnologias de IA e PLN | O projeto oferece a chance de aprender e aplicar ferramentas avançadas de reconhecimento de voz (ex.: Google Cloud Speech-to-Text) e processamento de linguagem natural. |
| 2     | Promover aprendizado multidisciplinar | O projeto envolve áreas como IA, engenharia de software e design de interfaces, garantindo que os membros adquiram conhecimentos em múltiplas disciplinas durante o desenvolvimento do protótipo. |
| 3     | Ampliação de nossa rede de contatos profissional | A interação com líderes da Comgás (ex.: Karina, Bruno, Gizele) cria oportunidades para conexões profissionais no setor de energia e inovação. |

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

## 2.7 Ideação

### 2.7.1 Brainstorming de Features

#### **Features Core (MVP - Sprint 2-4)**

**Análise de Qualidade:**
- **Análise de Cordialidade**: Detecção de tom respeitoso, uso de "por favor", "obrigado", tratamento adequado
- **Verificação de Script**: Checagem se protocolos obrigatórios foram seguidos (ex: orientações de segurança em vazamentos)
- **Análise de Tempo de Fala**: Proporção cliente vs atendente, tempo de resposta, pausas excessivas
- **Detecção de Interrupções**: Identificação de sobreposição de vozes e interrupções inadequadas
- **Score Básico (0-5)**: Algoritmo de pontuação baseado nos critérios da Comgás

**Interface Básica:**
- **Dashboard Simples**: Visualização de scores médios, distribuição de notas
- **Upload de áudio/transcrição**: Interface para carregar arquivos de ligação para análise
- **Relatório por ligação**: Detalhamento do score individual com justificativas

#### **Features Avançadas (Sprint 4-5)**

**Análise de Sentimento:**
- **Detecção de emoções**: Identificação de frustração, satisfação, irritação no cliente
- **Análise de palavrões**: Detecção automática de linguagem inadequada
- **Indicadores de conflito**: Identificação de escalation de tensão na conversa
- **Análise de satisfação**: Inferência do nível de satisfação do cliente

**Inteligência de Conteúdo:**
- **Extração de Palavras-chave**: Identificação automática de temas principais da ligação
- **Classificação por Tipo**: Categorização automática (reclamação, dúvida, solicitação, emergência)
- **Detecção de resolução**: Identificação se o problema foi resolvido na ligação
- **Análise de follow-up**: Verificação se foram dados próximos passos adequados
- **Extração de pontos de melhoria no serviço**: Identificação de situações recorrentes que podem indicar necessidade de melhoria nos produtos da Comgás

**Dashboard Avançado:**
- **Métricas agregadas**: Visão por período, atendente, tipo de ligação
- **Alertas automáticos**: Notificações para atendimentos com score muito baixo
- **Tendências**: Análise de evolução da qualidade ao longo do tempo
- **Filtros inteligentes**: Busca por critérios específicos (score, tipo, período)

#### **Features Futuras (Pós-MVP)**

**Integrações:**
- **API REST**: Integração com sistemas existentes da Comgás
- **Webhooks**: Notificações automáticas para sistemas terceiros
- **Exportação de Dados**: Relatórios em PDF, Excel, integração com BI

**IA Avançada:**
- **Aprendizado Contínuo**: Modelo que melhora com feedback dos analistas
- **Análise Preditiva**: Identificação de padrões que podem levar a problemas
- **Recomendações**: Sugestões específicas de melhoria por atendente
- **Benchmark Inteligente**: Comparação automática com padrões de excelência

### 2.7.2 Matriz de Priorização

| Feature | Impacto no Negócio | Viabilidade Técnica | Complexidade | Prioridade |
|---------|-------------------|-------------------|--------------|-----------|
| Score Básico (0-5) | Alto | Alta | Baixa | **P0** |
| Análise de cordialidade | Alto | Alta | Média | **P0** |
| Verificação de script | Alto | Média | Média | **P0** |
| Dashboard simples | Alto | Alta | Baixa | **P0** |
| Upload de áudio/transcrição | Alto | Alta | Baixa | **P0** |
| Análise de sentimento | Médio | Média | Alta | **P1** |
| Detecção de palavrões | Médio | Alta | Baixa | **P1** |
| Métricas agregadas | Médio | Média | Média | **P1** |
| Alertas automáticos | Médio | Média | Baixa | **P1** |
| Análise preditiva | Baixo | Baixa | Alta | **P2** |
| API REST | Baixo | Média | Média | **P2** |

**Legenda:**
- **P0**: MVP obrigatório (Sprint 3-4)
- **P1**: Features de valor agregado (Sprint 4-5)
- **P2**: Features futuras (pós-módulo)

### 2.7.3 Critérios de Priorização

**Impacto no Negócio:**
- Contribuição direta para o objetivo de monitorar 20% das ligações
- Redução de custo operacional
- Melhoria na qualidade do atendimento
- Facilidade de uso para analistas de qualidade

**Viabilidade Técnica:**
- Disponibilidade de dados de treinamento
- Complexidade de implementação em 10 semanas
- Recursos computacionais necessários
- Integração com stack tecnológica escolhida

**Critérios de Sucesso:**
- **Acurácia ≥ 80%** comparado à análise humana
- **Tempo de processamento < 2 minutos** por ligação
- **Interface intuitiva** para usuários não-técnicos
- **Escalabilidade** para volume de 30.000 ligações/mês (20% de 150k)

### 2.7.4 Roadmap de Implementação

**Sprint 2 (Semanas 3-4):**
- Definição de critérios de qualidade detalhados
- Prototipagem de algoritmos básicos
- Design de interface inicial

**Sprint 3 (Semanas 5-6):**
- Implementação do score básico
- Análise de cordialidade e script
- Dashboard MVP funcional

**Sprint 4 (Semanas 7-8):**
- Análise de sentimento
- Métricas agregadas
- Sistema de alertas

**Sprint 5 (Semanas 9-10):**
- Refinamentos e otimizações
- Testes de usabilidade
- Documentação e apresentação final

### 2.7.5 Considerações Estratégicas

**Diferencial Competitivo:**
- Foco específico em critérios da Comgás (vs soluções genéricas)
- Balanceamento entre automação e supervisão humana
- Escalabilidade sem aumento proporcional de custos

**Riscos e Mitigações:**
- **Risco**: Baixa acurácia inicial → **Mitigação**: Iteração com feedback de analistas
- **Risco**: Complexidade técnica → **Mitigação**: Priorização de features simples primeiro
- **Risco**: Resistência dos usuários → **Mitigação**: Design centrado no usuário e treinamento

**Alinhamento com Stakeholders:**
- **Bruno/Gizele (CX)**: Foco em insights acionáveis de qualidade
- **Ivan (Técnico)**: Conformidade com governança de dados
- **Karina (Inovação)**: Escalabilidade e inovação tecnológica


## 2.8 Canvas do Projeto

&emsp; Para deixar mais claro quem são as personas-alvo, a proposta de valor do Mínimo Produto Viável (MVP), suas principais funcionalidades, além de detalhes do projeto como custos e cronograma, criamos o Canvas do Mínimo Produto Viável. Ele ajuda a alinhar a equipe de desenvolvimento com os stakeholders e simplifica a validação da solução e seus detalhes com o parceiro. A figura do Canvas está logo abaixo.


<div align="center">
 <sub>Figura X - Canvas MVP </sub> <br>


 <img src="img/canvas-mvp.png" alt="Matriz de Riscos e Oportunidades" style="max-width: 800px; width: 100%; height: auto;">


 <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>




&emsp;Com o Canvas, dá pra explicar melhor pra quem a solução é voltada, quais jornadas ela atende, qual é a proposta de valor, as funcionalidades principais, os custos e o cronograma necessários, os resultados esperados e como vamos medir esses resultados. Isso torna a comunicação entre a equipe e as partes interessadas mais simples e direta, usando a representação visual do Canvas.

&emsp;A estimativa de R$ 82.500 considera que o MVP vai precisar de:

- Desenvolvimento: construir a transcrição automática de chamadas, análise de sentimento, dashboard e integração com os sistemas existentes.

- Infraestrutura: servidores e serviços de nuvem para processar áudio e textos, além de armazenamento dos dados.

- Gestão e ajustes: acompanhamento do projeto, testes e pequenas correções durante o desenvolvimento.

&emsp;Ou seja, o valor cobre toda a equipe, as ferramentas necessárias e o tempo estimado de 10 semanas para ter um MVP funcional, pronto para validar a solução com os usuários.

## 2.9 Matriz SWOT

<div align="center">
  <p> <b>Figura X </b> Matriz SWOT</p>
<img src="../documentos/img/SWOT.png" alt="matrix_swot" />
  <p><b>Fonte:</b>Material elaborado pela equipe (2025). </p>
</div>

### **FORÇAS** *(Fatores Internos Positivos)*
1. **Relevância regional**: A Comgás detém a maior parte da concessão da distribuição de gás canalizado no estado de São Paulo, garantindo estabilidade no mercado.  
2. **Infraestrutura consolidada**: Possui uma ampla rede de distribuição, com alta capilaridade em áreas urbanas e industriais.  
3. **Diversificação de clientes**: Atende desde residências até grandes indústrias, reduzindo a dependência de um único segmento.  
4. **Sólida reputação**: Reconhecida como uma das maiores distribuidoras de gás natural do Brasil, com histórico de operação eficiente.  
5. **Parcerias estratégicas**: Relacionamento com fornecedores como a [Petrobras (contrato de R$ 5,6 bilhões)](https://agencia.petrobras.com.br/w/petrobras-assina-contrato-com-a-comgas-no-valor-de-r-56-bilhoes).  

### **FRAQUEZAS** *(Fatores Internos Negativos)*
6. **Dependência regulatória**: Tarifas controladas pela Agência Reguladora (ARSESP), limitando flexibilidade de preços.  
7. **Custos elevados**: Manutenção da rede de distribuição e logística podem pressionar margens.  
8. **Concorrência indireta**: Gás engarrafado (GLP) e outras fontes de energia (elétrica, biomassa) competem no mercado.  
9. **Vulnerabilidade a crises de abastecimento**: Dependência de fornecedores únicos pode gerar instabilidade.  

### **OPORTUNIDADES** *(Fatores Externos Positivos)*
10. **Expansão do mercado de gás natural**: Abertura do mercado brasileiro (Novo Mercado de Gás) pode atrair novos investimentos.  
11. **Energia limpa**: Crescente demanda por combustíveis menos poluentes favorece o gás natural frente a derivados de petróleo.  
12. **Incentivos governamentais**: Programas como ["Gás para Crescer" (MME)](https://www.gov.br/mme/pt-br/assuntos/secretarias/petroleo-gas-natural-e-biocombustiveis/gas-para-crescer) podem impulsionar infraestrutura e consumo.  
13. **Crescimento industrial**: Expansão de polos industriais em SP aumenta demanda potencial ([Exemplo: Jundiaí](https://jundiai.sp.gov.br/noticias/2025/07/28/jundiai-atrai-novas-empresas-e-se-consolida-como-polo-industrial-tecnologico-e-logistico/)).  

### **AMEAÇAS** *(Fatores Externos Negativos)*
14. **Volatilidade de preços**: Flutuações no custo do gás natural no mercado internacional impactam custos.  
15. **Pressão por descarbonização**: Transição energética global pode reduzir a demanda por gás natural no longo prazo.  
16. **Regulação estrita**: Mudanças nas políticas governamentais podem afetar concessões e tarifas.  
17. **Concorrência de energias renováveis**: Solar e eólica tornam-se mais competitivas, especialmente para geração elétrica.  
18. **Crises econômicas**: Retração do setor industrial (principal cliente) reduz consumo.  

# 3. Requisitos do Projeto

## 3.1 Business Drivers

## Contexto da aplicação computacional

&emsp;Uma das principais missões da Comgás é o respeito pela experiência dos seus clientes, priorizando-se a atenção pelas suas dores, desejos e necessidades. Nesse contexto, a distribuidora de gás busca alcançar cada vez mais um nível de excelência no atendimento, o qual acompanhe a expansão de sua base de clientes. Apenas em 2024, foram concluídos mais de 13,5 milhões de atendimentos, sendo 94% realizados por canais digitais como WhatsApp e Chatbot. Em contrapartida, apenas 1% do quantitativo de atendimentos passou por uma avaliação dos clientes, necessitando-se uma análise retroativa manual da parcela restante, o que demanda tempo e custos. Logo, tem-se como desafio monitorar e melhorar a qualidade do atendimento em larga escala, sem aumentar custos operacionais.

&emsp;No presente projeto, será desenvolvido um sistema contendo um modelo que automatiza a avaliação dos atendimentos por meio de transcrições das ligações telefônicas e históricos de conversas, com uso de Processamento de Linguagem Natural (PLN), análise de sentimentos e critérios objetivos de qualidade. Essa solução permitirá ampliar significativamente a amostragem de atendimentos avaliados, gerar insights mais precisos e replicáveis em relação ao desempenho dos atendentes e letramento de clientes, bem como auxiliará a retroalimentar os canais digitais — como a assistente virtual "Cris" e o bot dentro do site oficial — com dados que podem melhorar a performance e a eficiência do atendimento automatizado.

&emsp;O projeto busca atender a diversos business drivers estratégicos da Comgás, como o aumento da retenção de clientes por meio da resolução eficaz das demandas/reclamações via digital; a agilidade no atendimento a partir da identificação de gargalos e otimização de respostas; e a qualidade do atendimento, avaliada com base em critérios objetivos como fidelidade ao protocolo, cordialidade, fluidez, além de demais critérios a serem definidos pela empresa; o controle e auditoria do negócio, ampliando a abrangência e a precisão na análise das interações, bem como, por fim, a solução implica numa análise estruturada da opinião pública, ao capturar sentimentos e percepções dos clientes expressas durante as chamadas, oferecendo dados valiosos para ajustes estratégicos e aprimoramento contínuo da experiência do cliente.

&emsp;A seguir, tem-se a demonstração de dois fluxos atuais de trabalho em relação às análises de qualidade de atendimento ao cliente, bem como sugestões de melhorias a partir da implementação do sistema a ser desenvolvido no projeto. Todos os fluxos foram produzidos a partir da plataforma PlantUML.

&emsp;Neste primeiro caso, observa-se os passos rotineiros de um analista de qualidade de atendimentos em chamadas, que performa as análises de forma totalmente manual. Então, tem-se o mesmo cenário após a implementação da plataforma de análises automatizadas dos atendimentos ao cliente.


<div align="center">
<sub>Figura x - Análise de Qualidade</sub><br>
<img src="img/analista_qualidade.png"><br>
<sup>Fonte: Material produzido pelos autores (2025) </sup>
</div>


&emsp;Neste segundo caso, observa-se os passos rotineiros de um colaborador de BackOffice durante a resolução de um atendimento.

### Antes da Automação
- Casos são encaminhados ao BackOffice com protocolos soltos.
- Dificuldade em identificar palavras-chave ou falhas específicas.
- Processo lento e sujeito a erros.
- Baixo reaproveitamento de dados para melhoria.

&emsp;Então, tem-se o mesmo contexto após o uso da plataforma de análises automatizadas dos atendimentos ao cliente pelo time de BackOffice.

### Depois da Automação com PLN
- O BackOffice recebe relatórios com tags automáticas.
- PLN destaca termos como "vazamento", "falta de gás", "leitura incorreta".
- Verificação automática de aderência ao script técnico.
- Visualização rápida de onde houve falha na conversa.
- Equipe atua direto nos casos críticos com mais agilidade.

<div align="center">
<sub>Figura x - Profissional BackOffice</sub><br>
<img src="img/BOFF.png"><br>
<sup>Fonte: Material produzido pelos autores (2025) </sup>
</div>

## 3.2 Requisitos Funcionais (RFs)

&emsp;Nessa seção constam os requisitos funcionais da solução.

<div align="center">
<sub>Tabela X - Requisitos Funcionais</sub><br>

| ID   | User Story (Requisito Funcional) | Descrição dos Testes |
|------|----------------------------------|----------------------|
| **RF01** | **Ingestão e Transcrição Automática**<br>Como **analista de qualidade**, quero que o sistema **consuma áudios de chamadas (simuladas ou reais anonimizadas)** e gere **transcrições com carimbo de data/hora por trecho**, para permitir auditoria e análise detalhada. | **Pré-condição:** Endpoint de upload/ingestão publicado; credenciais válidas; áudios `.wav`/`.mp3` disponíveis e anonimizados.<br>**Procedimento:** (1) Enviar chamadas via API/Upload nos formatos suportados; (2) Acompanhar fila de processamento; (3) Abrir a transcrição gerada; (4) Verificar segmentação por trechos e *timestamps*.<br>**Resultado Esperado:** (a) API aceita `.wav` e `.mp3` e retorna `201 Created`; (b) Transcrição segmentada com *timestamps* por trecho; (c) Metadados salvos (duração, canal, agente, data); (d) Logs de processamento registrados.<br>**Pós-condição:** Arquivo armazenado; transcrição indexada para busca/filtros; evidências disponíveis para auditoria. |
| **RF02** | **Classificação por Intenção e Etiquetas**<br>Como **integrante de qualidade**, quero **classificar e filtrar chamadas por intenção** (vazamento, falta de gás, conta, etc.) e por **severidade** (grave, moderado, simples) para analisar a qualidade por contexto. | **Pré-condição:** Taxonomia de intenções e severidades configurada; rótulos habilitados no admin.<br>**Procedimento:** (1) Processar lote de chamadas transcritas; (2) Verificar rótulo de **intenção primária** e, quando aplicável, **secundária**; (3) Aplicar filtros por data, canal, fila, agente, intenção e severidade.<br>**Resultado Esperado:** (a) Chamadas rotuladas automaticamente com intenção primária/possível secundária; (b) Etiquetas configuráveis aplicadas; (c) Filtros retornam subconjuntos consistentes e exportáveis.<br>**Pós-condição:** Rótulos persistidos e auditáveis; filtros salvos por usuário/equipe. |
| **RF03** | **Resultado de Atendimento (Resolvida vs Não)**<br>Como **time de sucesso do cliente**, quero ver **quantas ligações foram resolvidas e não resolvidas**, por período e filtros, para entender fatores que aumentam a **taxa de resolução**. | **Pré-condição:** Regra de derivação do **status de resolução** (ex.: presença de confirmação/fechamento em roteiro) ou integração com CRM; dados históricos carregados.<br>**Procedimento:** (1) Executar rotinas de categorização de status; (2) Abrir painel por período e filtros (intenção/etiqueta/agente/canal); (3) Conferir KPIs.<br>**Resultado Esperado:** (a) Campo **status de resolução** atribuído corretamente; (b) KPIs calculados: **% resolvida**, **tempo médio até resolução**, **resolução por intenção/etiqueta/agente**; (c) Séries temporais e ranking por causa/área.<br>**Pós-condição:** KPIs armazenados para comparativos e tendências. |
| **RF04** | **Score de Qualidade (0–5) por Checklists**<br>Como **time de qualidade**, quero um **score automático de 0–5** por ligação com base em critérios: cordialidade, cumprimento de script, tempo de fala, interrupções, menções obrigatórias e qualidade técnica. | **Pré-condição:** Checklist parametrizado e pesos definidos; modelo/heurísticas ativas.<br>**Procedimento:** (1) Processar ligações; (2) Abrir tela de avaliação; (3) Conferir **evidências** (trechos da transcrição) que justificam cada ponto; (4) Visualizar agregados (média, mediana, desvio-padrão).<br>**Resultado Esperado:** (a) Score **0–5** calculado; (b) Evidências textuais por critério; (c) Painel com **média/mediana/desvio-padrão** por período/filtro; (d) Pesos ajustáveis refletem no score imediatamente.<br>**Pós-condição:** Scores e evidências persistidos para auditoria/coaching. |
| **RF05** | **Erros de Instrução e Lacunas de Conhecimento**<br>Como **qualidade**, quero **identificar chamadas** em que o agente deu **instrução incorreta** ou **não soube contornar**, para direcionar treinamento e atualizar a base de conhecimento. | **Pré-condição:** Scripts e políticas carregados; regras/NLP para detecção (contradição, *disclaimers* ausentes, linguagem inadequada) habilitadas.<br>**Procedimento:** (1) Rodar detecção em lote; (2) Abrir dashboard de tópicos; (3) Inspecionar chamadas **flagadas** e evidências; (4) Ver **sentimento por trecho**, **interrupções/overlap** e **correlação** entre sentimento, score e resolução (scatter/heatmap).<br>**Resultado Esperado:** (a) **Flag** “instrução incorreta/insuficiente” com evidência textual; (b) Ranking de tópicos com maior incidência; (c) Métricas de **interrupção/overlap** e **linguagem inadequada**; (d) Visual de correlação básico entre **sentimento × score × resolução**.<br>**Pós-condição:** Casos priorizados para *coaching* e atualização da base de conhecimento. |
| **RF06** | **Dashboard de Performance e Comparativos**<br>Como **liderança de CX**, quero um **dashboard executivo** com: score médio, cobertura analisada, % resolvida, **top intenções** com pior desempenho, **tendência temporal** e **comparativo** entre times/agentes. | **Pré-condição:** Pipeline de dados consolidado; perfis e permissões configurados.<br>**Procedimento:** (1) Abrir dashboard; (2) Aplicar **filtros rápidos** (data, intenção, severidade, agente, fila, canal); (3) Exportar CSV; (4) Usar **drill-down** para a chamada detalhada.<br>**Resultado Esperado:** (a) Cards com KPIs atualizados; (b) Gráficos de barras/linha; (c) Exportação **CSV** disponível; (d) Links de **drill-down** levam à visão da chamada.<br>**Pós-condição:** Filtros e *views* salvos; artefatos exportados armazenados. |
| **RF07** | **Visualização Detalhada da Chamada e Auditoria**<br>Como **analista**, quero um **player/linha do tempo** com *highlights* por evento (palavra-chave, interrupção, descumprimento de script), **ajuste manual de rótulos** com trilha de auditoria e **download** de transcrição/laudo em **PDF/CSV**. | **Pré-condição:** Chamada processada (transcrição, rótulos e eventos gerados).<br>**Procedimento:** (1) Abrir player; (2) Navegar por *highlights*; (3) Ajustar rótulos (override) e salvar; (4) Baixar transcrição e laudo em PDF/CSV.<br>**Resultado Esperado:** (a) Linha do tempo com eventos destacados; (b) **Override** aplicado com **trilha de auditoria** (quem, quando, o quê); (c) Downloads gerados com conteúdo íntegro.<br>**Pós-condição:** Alterações persistidas; auditoria disponível para consulta. |
| **RF08** | **Validação Automático vs Humano**<br>Como **time de qualidade**, quero **comparar as avaliações automáticas** com uma **amostra humana** para medir acurácia e **calibrar o modelo**. | **Pré-condição:** Amostragem definida; dupla revisão humana ativa (inter-rater); versão de modelo registrada.<br>**Procedimento:** (1) Selecionar amostra; (2) Coletar dupla rotulagem humana; (3) Comparar com saída automática; (4) Gerar relatório de **precisão/recall/F1** por critério e por intenção, além de **matriz de confusão** para “resolvida vs não”.<br>**Resultado Esperado:** (a) Relatório com **precisão/recall/F1** por critério/intenção; (b) Matriz de confusão “resolvida vs não”; (c) Registro de versão de modelo e data de implantação para rastreabilidade.<br>**Pós-condição:** Evidências para *tuning* do modelo e plano de calibração registrados. |

<sup>Material produzido pelos autores (2025)</sup>
</div><br>

  
## 3.3 Requisitos Não Funcionais (RNFs)

&emsp;Os Requisitos Não Funcionais (RNFs) especificam as qualidades e restrições que o sistema deve respeitar, como desempenho, segurança, usabilidade, manutenibilidade, entre outros. Diferentemente dos RFs, os RNFs não dizem respeito ao comportamento direto do sistema, mas à forma como esse comportamento deve ser entregue. Neste projeto, os RNFs foram definidos com base nas diretrizes da norma ISO/IEC 25010 e abordam critérios técnicos essenciais para garantir robustez, escalabilidade e boa experiência de uso.

<div align="center">
<sub>Tabela X - Requisitos não funcionais</sub><br>

| **RNF#** | **Descrição**                                                                                                                      | **Prioridade** | **Atributo ISO/IEC 25010** |
| -------- | ---------------------------------------------------------------------------------------------------------------------------------- | -------------- | -------------------------- |
| RNF01    | O sistema deve ser desenvolvido com arquitetura modular para facilitar alterações e substituição de componentes.                  | Alta           | Manutenibilidade           |
| RNF02    | O código deve seguir um padrão de formatação definido (ex.: ESLint/Prettier para JS, Black para Python).                           | Baixa          | Manutenibilidade           |
| RNF03    | Alterações no código devem ser versionadas com Git e documentadas em commits no formato _Conventional Commits_.                    | Alta           | Manutenibilidade           |
| RNF04    | O projeto deve poder ser executado localmente com um único comando (ex.: `docker-compose up`).                                     | Alta           | Portabilidade              |
| RNF05    | O pipeline de NLP deve rodar localmente sem necessidade de serviços externos pagos.                                                | Alta            | Portabilidade              |
| RNF06    | O modelo de NLP deve atingir pelo menos 80% de acurácia no conjunto de testes interno.                                             | Baixa          | Funcionalidade (Acurácia)  |
| RNF07    | O projeto deve incluir pelo menos 80% de cobertura de testes automatizados para código crítico.                                    | Média          | Confiabilidade             |
| RNF08    | Erros críticos devem ser tratados com mensagens claras, sem stack trace exposto ao usuário final.                                  | Alta          | Usabilidade                |
| RNF09    | O dashboard deve ser intuitivo, permitindo que um novo usuário realize a primeira análise em até 3 minutos sem treinamento formal. | Alta           | Usabilidade                |
| RNF10    | O tempo de carregamento inicial não deve ultrapassar 5 segundos em máquina local com configuração mínima pré-definida.             | Baixa          | Desempenho/Eficiência      |
| RNF11    | Mensagens de status e progresso devem ser exibidas durante o processamento.                                                        | Alta           | Usabilidade                |
| RNF12    | O sistema deve ser empacotado em container Docker, garantindo execução consistente em qualquer ambiente que suporte Docker Engine. | Alta           | Portabilidade              |
| RNF13    | Dependências e versões de bibliotecas devem estar listadas em arquivo de configuração (ex.: `requirements.txt`, `package.json`).  | Alta           | Manutenibilidade           |
| RNF14    | As tags classificativas de intenção do atendimento e o feedback de cores devem ter tamanho e contrastes cromáticos otimizados para garantir atenção imediata do usuário, facilitando a interpretação rápida do contexto do atendimento e do resultado da análise de qualidade. | Alta | Usabilidade |
| RNF15    | Ao acessar a transcrição do atendimento, o score automático de qualidade deve ser exibido em tela em até 4 segundos após o carregamento da página. | Alta | Desempenho/Eficiência |

<sup>Material produzido pelos autores (2025)</sup>

</div><br>


### Requisitos Não Funcionais Definidos como User Stories

<div align="center">
<sub>Tabela X - User Stories dos Requisitos Não Funcionais</sub><br>

| ID    | User Story                                                                                                                                                                                                                              |
| ----- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| RNF01 | **Como** desenvolvedor, **quero** uma arquitetura modular, **para que** seja fácil modificar ou substituir componentes sem afetar o restante do sistema.                                                                               |
| RNF02 | **Como** desenvolvedor, **quero** que o código siga um padrão de formatação, **para que** a leitura e manutenção sejam consistentes.                                                                                                    |
| RNF03 | **Como** desenvolvedor, **quero** que todas as alterações sejam versionadas com Git e commits intuitivos, **para que** o histórico do projeto seja rastreável.                                                                         |
| RNF04 | **Como** desenvolvedor, **quero** executar o projeto localmente com um único comando, **para que** a inicialização seja simples e rápida.                                                                                               |
| RNF05 | **Como** engenheiro de ML, **quero** que o NLP rode localmente sem depender de serviços pagos, **para que** eu possa desenvolver e testar sem custos extras.                                                                            |
| RNF06 | **Como** analista de qualidade, **quero** que o modelo atinja 80% de acurácia, **para que** as análises sejam confiáveis.                                                                                                               |
| RNF07 | **Como** gerente de desenvolvimento, **quero** que haja pelo menos 80% de cobertura de testes no código crítico, **para que** possamos reduzir falhas.                                                                                  |
| RNF08 | **Como** usuário, **quero** que erros críticos exibam mensagens claras, **para que** eu entenda o que ocorreu sem ver informações técnicas confusas.                                                                                    |
| RNF09 | **Como** novo usuário, **quero** realizar minha primeira análise sem treinamento em até 5 minutos, **para que** eu possa usar o sistema de forma intuitiva.                                                                            |
| RNF10 | **Como** usuário, **quero** que o dashboard carregue em até 5 segundos, **para que** eu não perca tempo esperando.                                                                                                                      |
| RNF11 | **Como** usuário, **quero** ver mensagens de status e progresso durante o processamento, **para que** eu saiba que o sistema está funcionando.                                                                                          |
| RNF12 | **Como** desenvolvedor do sistema, **quero** que ele seja empacotado em um container Docker **para** garantir execução consistente em qualquer ambiente que suporte Docker Engine.                                                      |
| RNF13 | **Como** desenvolvedor, **quero** que as dependências estejam listadas em arquivo de configuração, **para que** seja fácil instalar e reproduzir o ambiente.                                                                           |
| RNF14 | **Como** analista, **quero** tags e cores destacadas **para** entender rapidamente a intenção e qualidade do atendimento.                                                                                                               |
| RNF15 | **Como** usuário, **quero** ver o score de qualidade imediatamente ao abrir uma transcrição **para** entender rapidamente a qualidade do atendimento.                                                                                   |

<sup>Material produzido pelos autores (2025)</sup>

</div><br>


### Tabela de Testes para Requisitos Não Funcionais

<div align="center">
<sub>Tabela X - Testes associados aos Requisitos Não Funcionais</sub><br>

| Caso de Teste | RNF#  | Descrição do Teste                                                                                         | Pré-condição                                    | Caso de Aceite                                                                                     | Caso de Recusa                                                                       |
| ------------- | ----- | ----------------------------------------------------------------------------------------------------------| ------------------------------------------------ | -------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------ |
| CT-RNF01      | RNF01 | Desativar ou substituir um módulo e verificar funcionamento dos demais.                                   | Sistema modular implementado.                   | Demais módulos continuam funcionando sem erros.                                                    | Falhas ou travamentos em outros módulos.                                             |
| CT-RNF02      | RNF02 | Rodar verificação de estilo de código com ferramenta configurada.                                         | Ferramenta de lint instalada.                    | Código segue padrão definido sem erros.                                                            | Erros de formatação detectados.                                                      |
| CT-RNF03      | RNF03 | Verificar histórico de commits no repositório Git.                                                         | Repositório Git atualizado.                      | Todos os commits possuem mensagens claras e rastreáveis, com prefixos de "docs", "fix", "feat", etc.| Histórico confuso ou sem padronização.                                               |
| CT-RNF04      | RNF04 | Executar o comando de inicialização local e verificar funcionamento.                                      | Ambiente local configurado.                      | Sistema inicia corretamente com um único comando.                                                  | Necessidade de múltiplos comandos ou erros de inicialização.                         |
| CT-RNF05      | RNF05 | Executar análise NLP sem conexão à internet e verificar funcionamento.                                     | Ambiente local preparado.                         | NLP funciona offline corretamente.                                                                 | Falha ou dependência de serviço externo pago.                                        |
| CT-RNF06      | RNF06 | Rodar modelo de NLP no conjunto de teste interno e calcular acurácia.                                     | Conjunto de teste disponível.                    | Acurácia ≥ 80%.                                                                                     | Acurácia < 80%.                                                                      |
| CT-RNF07      | RNF07 | Rodar ferramenta de cobertura de testes.                                                                  | Testes automatizados implementados.               | Cobertura ≥ 80% no código crítico.                                                                  | Cobertura < 80%.                                                                     |
| CT-RNF08 | RNF08 | Forçar erro crítico e verificar mensagem exibida. | Sistema em execução. | Mensagem clara exibida sem stack trace. | Mensagem genérica ou com stack trace exposto. |
| CT-RNF09      | RNF09 | Criar conta de teste e medir tempo até primeira análise.                                                  | Sistema disponível.                               | Primeira análise concluída em ≤ 3 minutos sem ajuda externa.                                        | Tempo maior que 3 minutos ou necessidade de treinamento.                             |
| CT-RNF10      | RNF10 | Medir tempo de carregamento inicial do dashboard.                                                         | Máquina local com configuração mínima.            | Carregamento ≤ 5 segundos.                                                                          | Tempo maior que 5 segundos.                                                          |
| CT-RNF11      | RNF11 | Executar análise e verificar se mensagens de progresso são exibidas.                                      | Sistema em execução.                              | Mensagens claras exibidas durante todo o processo.                                                  | Ausência de mensagens de status.                                                     |
| CT-RNF12      | RNF12 | Executar o container Docker em pelo menos dois ambientes diferentes (Windows com Docker Desktop e Linux). | Docker instalado e ambiente configurado nos dois SOs. | O sistema inicia e funciona corretamente com mesmas configurações e resultados em ambos os ambientes.| Docker falha ao iniciar ou apresenta comportamento diferente entre os ambientes.     |
| CT-RNF13      | RNF13 | Instalar dependências a partir do arquivo de configuração e executar sistema.                             | Arquivo de configuração completo.                 | Sistema instala e executa corretamente.                                                             | Dependências ausentes ou falha na instalação.                                        |
| CT-RNF14      | RNF14 | Verificar tamanho mínimo das tags classificativas.                                                        | Acessar tela de atendimento em desktop (resolução 1920x1080). | Tags ocupam ≥5% da área útil da tela.                                                                | Tags com tamanho inferior ao especificado.                                           |
| CT-RNF15      | RNF15 | Verificar exibição do score em ≤4 segundos.                                                               | Acessar transcrição com cálculo pré-processado.   | Score exibido em ≤4s.                                                                                | Tempo excedido em mais que 4s.                                                       |

<sup>Material produzido pelos autores (2025)</sup>

</div><br>

## 3.4 Correlação RFs e RNFs

&emsp;Nesta seção, apresentamos a matriz de correlação entre os **Requisitos Funcionais (RFs)** já definidos e os **Requisitos Não Funcionais (RNFs)** listados.  
&emsp;Cada marcação **✓** indica a necessidade de observância do respectivo RNF para o correto funcionamento e/ou boa experiência do RF correlato.

<div align="center">
<sub>Tabela X - Relação de Requisitos Funcionais e Não Funcionais</sub><br>

| **RF / RNF** | **RNF01**<br>(Arquitetura modular) | **RNF02**<br>(Padrão de formatação) | **RNF03**<br>(Conventional Commits) | **RNF04**<br>(Execução local 1 comando) | **RNF05**<br>(NLP local) | **RNF06**<br>(Acurácia ≥80%) | **RNF07**<br>(Cobertura testes ≥80%) | **RNF08**<br>(Tratamento de erros) | **RNF09**<br>(Onboarding ≤3min) | **RNF10**<br>(Carga inicial ≤3s) | **RNF11**<br>(Status/Progresso) | **RNF12**<br>(Container Docker) | **RNF13**<br>(Deps versionadas) | **RNF14**<br>(Tags/cores acessíveis) | **RNF15**<br>(Score em ≤4s) |
|---|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|
| **RF01 – Ingestão e Transcrição Automática** | ✓ |  |  | ✓ | ✓ |  | ✓ | ✓ |  |  | ✓ | ✓ | ✓ |  |  |
| **RF02 – Classificação por Intenção e Etiquetas** | ✓ |  |  |  | ✓ | ✓ | ✓ | ✓ |  |  | ✓ | ✓ | ✓ | ✓ |  |
| **RF03 – Resultado de Atendimento (Resolvida vs Não)** | ✓ |  |  |  |  | ✓ |  | ✓ |  |  | ✓ | ✓ | ✓ |  |  |
| **RF04 – Score de Qualidade (0–5) por Checklists** | ✓ |  |  |  | ✓ | ✓ | ✓ | ✓ |  |  | ✓ | ✓ | ✓ |  | ✓ |
| **RF05 – Erros de Instrução e Lacunas de Conhecimento** | ✓ |  |  |  | ✓ | ✓ | ✓ | ✓ |  |  | ✓ | ✓ | ✓ |  |  |
| **RF06 – Dashboard de Performance e Comparativos** | ✓ |  |  |  |  |  |  | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |  |
| **RF07 – Visualização Detalhada e Auditoria** | ✓ |  |  |  |  |  |  | ✓ |  | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| **RF08 – Validação Automático vs Humano** | ✓ |  |  |  |  | ✓ | ✓ | ✓ |  |  | ✓ | ✓ | ✓ |  |  |

<sup>Material produzido pelos autores (2025)</sup>
</div><br>

&emsp;Observação: RNF02 e RNF03 são requisitos **transversais** de engenharia e não foram associados a RFs específicos na matriz por não influenciarem diretamente uma funcionalidade isolada, mas sim a **manutenibilidade e rastreabilidade** do projeto como um todo.

## 3.5 Casos de Uso

&emsp;Os **casos de uso** representam uma técnica de modelagem amplamente utilizada na engenharia de software para descrever, de forma clara e objetiva, como um sistema interage com seus usuários (atores) e outros sistemas externos. Cada caso de uso especifica um conjunto de ações que o sistema deve executar para gerar um resultado de valor observável para um ator específico.,

&emsp;No contexto deste projeto, cada caso de uso está vinculado a um requisito funcional, garantindo rastreabilidade entre a modelagem e as funcionalidades previstas no sistema.


<div align="center">
<sub>Figura 17 - Diagrama de Caso de Uso RF01</sub>  
<img src="./img/caso_de_uso_RF01.svg" alt="Imagem com o diagrama de caso de uso RF01" width="100%">  
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura 18 - Diagrama de Caso de Uso RF02</sub>  
<img src="./img/caso_de_uso_RF02.svg" alt="Imagem com o diagrama de caso de uso RF02" width="100%">  
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura 19 - Diagrama de Caso de Uso RF03</sub>  
<img src="./img/caso_de_uso_RF03.svg" alt="Imagem com o diagrama de caso de uso RF03" width="100%">  
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura 20 - Diagrama de Caso de Uso RF04</sub>  
<img src="./img/caso_de_uso_RF04.svg" alt="Imagem com o diagrama de caso de uso RF04" width="100%">  
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

## 3.6 Visão inicial sobre a Solução Técnica de Engenharia 

### 3.6.1 Arquitetura da Solução Proposta

&emsp; A arquitetura da solução proposta para o monitoramento de qualidade de chamadas é organizada em um fluxo de processamento claro, dividido em quatro camadas principais, como mostrado na figura abaixo. Essa estrutura permite uma gestão organizada de cada etapa do projeto.

<div align="center">
  <sub>Figura X - Solução Técnica de Engenharia </sub> <br>

  <img src="img/solucao-tecnica.png" alt="Solução Técnica de Engenharia" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>


#### Camada de Interface

&emsp; Esta camada é o ponto de interação com o usuário final e a porta de entrada para os dados.

- **Gravações de Chamadas**: Representa o ponto de entrada da solução. Os arquivos de áudio das ligações telefônicas são o que alimenta o sistema.
- **Dashboard de Resultados**: É a interface de saída, onde os dados processados são apresentados. Ele exibe as pontuações agregadas e outras métricas de desempenho de forma visual e intuitiva para os analistas de qualidade.

#### Processamento e Serviços

&emsp; Esta camada concentra os serviços técnicos essenciais para a transformação inicial dos dados.

- **Serviço de ASR (Áudio para Texto)**: Componente central para a transcrição. Ele utiliza a tecnologia de Reconhecimento Automático de Fala (ASR) para converter o áudio das chamadas em texto, permitindo a análise subsequente.

#### Camada de Lógica de Negócio

&emsp; Esta é a camada que contém a inteligência e as regras do projeto. É aqui que os dados são transformados em insights acionáveis.

- **Análise de Qualidade**: Utiliza o texto transcrito para aplicar os critérios de qualidade do atendimento através de subcomponentes específicos, como por exemplo:
  - **Análise de Cordialidade**: Avalia o uso de tratamento respeitoso, saudações e cortesia
  - **Análise de Sentimento**: Identifica o tom da conversa (positivo, negativo, neutro)
  - **Verificação de Script**: Verifica cumprimento de roteiros obrigatórios e orientações específicas
 
- **Gerar Pontuação Final**: Com base nos dados da análise de qualidade, este componente atribui uma pontuação objetiva para cada ligação. Essa pontuação é um resumo numérico da performance do atendimento.

#### Camada de Dados

&emsp; Esta camada é responsável pela persistência e pelo gerenciamento dos dados, bem como pelo ciclo de validação.

- **Armazenamento de Dados**: Local onde os resultados do processamento (transcrições, pontuações, etc.) são guardados de forma estruturada. É a fonte de dados para o dashboard e para a validação.
- **Validação Humana (quando necessário)**: Representa o processo de auditoria. Uma amostra das ligações é enviada para um analista humano, que as avalia manualmente. O resultado dessa avaliação é comparado com a pontuação gerada pela solução, servindo como um feedback crucial para aprimorar e validar a precisão da inteligência artificial.

### 3.6.2 APIs

#### API de Speech-to-Text (STT)

&emsp; A conversão de áudio em texto será realizada pelo **Whisper**, implementado dentro de um contêiner no **Amazon SageMaker**.  

&emsp;Essa abordagem foi escolhida por garantir maior conformidade com a **LGPD**, já que o processamento ocorre em ambiente controlado dentro da nuvem AWS. Além disso, o Whisper apresenta alta precisão, suporte a múltiplos idiomas e robustez no processamento de áudios de diferentes qualidades.  

&emsp;Ao utilizá-lo via SageMaker, é possível integrar o modelo como um endpoint de inferência, o que permite que as requisições sejam feitas por meio de **APIs próprias do SageMaker**, garantindo escalabilidade, monitoramento e segurança.

**Detalhes Técnicos**

- Serviço: Amazon SageMaker (contêiner com Whisper)  
- Endpoint: `{SAGEMAKER_ENDPOINT_URL}`  
- Método HTTP: `POST`  

**Headers obrigatórios:**
- `Authorization: Bearer {TOKEN_DE_AUTENTICACAO}`  
- `Content-Type: multipart/form-data`,

**Corpo da Requisição**

  `multipart/form-data` com os seguintes parâmetros:

  | Parâmetro | Tipo  | Obrigatório | Descrição                                    |
  |-----------|-------|-------------|----------------------------------------------|
  | file      | file  | Sim         | Arquivo de áudio da chamada.                 |
  | model     | string| Sim         | Nome do modelo de transcrição. Exemplo: whisper-1. |
  | language  | string| Opcional    | Código ISO do idioma. Para português, use `pt`. |


  **Exemplo de Requisição (cURL)**

  ```bash
  curl -X POST "https://api.openai.com/v1/audio/transcriptions" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
    -H "Content-Type: multipart/form-data" \
    -F "file=@chamada.mp3" \
    -F "model=whisper-1" \
    -F "language=pt"
  ```

  **Respostas**

  Sucesso – 200 OK
  ```
  {
    "text": "Olá Comgás, meu nome é Maria e eu preciso de ajuda com a minha conta."
  }
  ```
  Erro – 401 Unauthorized
  ```
  {
    "error": "Invalid API key provided."
  }
  ```
  Erro – 400 Bad Request
  ```
  {
    "error": "Arquivo inválido ou parâmetro ausente."
  }
  ```

#### API para Recebimento de Áudios

&emsp; Essa serve como a porta de entrada do sistema. Ela recebe arquivos de áudio enviados pela interface de usuário ou por sistemas internos da Comgás, valida o formato e inicia um processo de análise **assíncrono**.  
A API responde imediatamente com um `job_id` para que o cliente possa acompanhar o status da solicitação.

---

**Detalhes Técnicos**

- Endpoint: `/api/v1/analise/audio`  
- Método HTTP: `POST`  

**Headers obrigatórios:**
- `Content-Type: multipart/form-data`

**Corpo da Requisição**

`multipart/form-data` com os seguintes parâmetros:

| Parâmetro   | Tipo | Obrigatório | Descrição                      |
|-------------|------|-------------|--------------------------------|
| audio_file  | file | Sim         | Arquivo de áudio da chamada.   |

---

**Restrições**

- Formatos suportados: `.wav`, `.mp3`, `.flac`  
- Tamanho máximo: 25 MB  
- Processamento: assíncrono (a resposta inicial apenas confirma o recebimento)  

---

**Exemplo de Requisição (cURL)**

```bash
curl -X POST "https://seu-dominio.com/api/v1/analise/audio" \
  -H "Content-Type: multipart/form-data" \
  -F "audio_file=@ligacao_cliente.wav"
```

**Respostas**

Sucesso – 202 Accepted
```
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "Processamento iniciado. Use o job_id para verificar o status da análise."
}
```
Erro – 400 Bad Request
```
{
  "error": "Arquivo inválido. Formato não suportado ou excedeu o limite de 25MB."
}
```
Erro – 500 Internal Server Error
```
{
  "error": "Erro interno ao processar a solicitação."
}
```

**Notas de Implementação**

&emsp;A arquitetura assíncrona garante que o front-end não fique bloqueado enquanto o áudio é processado. 
O `job_id` poderá ser usado em um endpoint futuro para consultar o status da análise.
Essa abordagem suporta escalabilidade para lidar com muitas de chamadas simultâneas.

# 4. Modelagem de Dados

&emsp;A modelagem de dados foi guiada pelos usos reais do sistema: abertura de atendimentos, registro de chamadas e consulta de histórico.

&emsp;No modelo conceitual, a entidade Cliente e a entidade Atendente se relacionam com Atendimento em uma relação de 1:N, sendo que cada Atendimento contém diversas Chamadas (1:N).

&emsp;No modelo lógico, considerando o uso do MongoDB, a entidade Atendimento armazena apenas o resumo das informações (como status, métricas e última chamada), de modo a possibilitar listagens rápidas. Já os detalhes ficam registrados na entidade Chamadas, incluindo timestamps, mensagens e metadados.

&emsp;Essa abordagem garante consultas simplificadas (geralmente 1–2 operações), utilização eficiente do espaço e maior facilidade para evolução do esquema sem impactos significativos na aplicação.

## 4.1 Especificação da Base de Dados para Modelo de Recomendação

&emsp;A **Comgás** solicitou o desenvolvimento de um modelo de _Machine Learning (ML)_ destinado a avaliar automaticamente a qualidade das chamadas de atendimento, em substituição à auditoria manual baseada em critérios internos de qualidade (por exemplo: saudação, identificação, segurança/protocolo, orientação, clareza e fechamento).  

- **Grão de dados:** 1 registro por chamada, associado a atendimento, cliente e atendente.  
- **Entradas:** transcrição (texto), metadados (canal, duração, horários) e sinais dos critérios de avaliação da Comgás (checklist capturado por regras e extrações no texto).  
- **Rótulo/Alvo:** `nota_qualidade` (em escala de 0–100 ou 1–5). Quando disponíveis, são utilizadas notas humanas históricas em abordagem supervisionada; na ausência dessas notas, são gerados *weak labels* a partir do checklist.  
- **Features:** palavras-chave e *embeddings* do texto, presença e ordem de passos de segurança, tempo de fala por participante, interrupções, períodos de silêncio e indicadores de sentimento/entonação (quando disponíveis).  
- **Pipeline:** ingestão dos dados a partir do MongoDB → limpeza e anonimização → engenharia de *features* → treino e validação do modelo → gravação da predição na coleção de chamadas (`quality.score`, `quality.version`, `quality.explain`).  
- **Métricas de sucesso:** erro médio absoluto (MAE) e raiz do erro quadrático médio (RMSE) em relação às notas humanas; coeficiente kappa de Cohen (nível de acordo com auditores); cobertura por canal; e detecção de *drift* ao longo do tempo.  

&emsp;Essa especificação assegura que o modelo aprenda a partir dos critérios já utilizados pela Comgás, preserve a rastreabilidade do processo e permita a evolução das regras de avaliação sem comprometer o histórico.  


## 4.2 Modelo Conceitual de Dados

<div align="center">
  <sub>Figura X - Modelo Conceitual</sub><br>
  <img src="../documentos/img/modelo_conceitual.png" alt="Modelo Conceitual" width="100%"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp;O modelo de dados foi estruturado para suportar operações de abertura de atendimentos, registro de chamadas e consulta de histórico, garantindo flexibilidade e eficiência em um ambiente **NoSQL** (MongoDB).  

&emsp;As principais entidades do sistema são **Cliente**, **Atendente**, **Atendimento** e **Chamadas**.  

- **Cliente:** representa o usuário que solicita atendimento. Possui atributos `id`, `name` e `role`.  
- **Atendente:** representa o agente responsável pelo atendimento. Possui atributos `id`, `name` e `role`.  
- **Atendimento:** conecta cliente e atendente, armazenando informações como `id`, `clientId`, `atendenteId`, `subject`, `status`, `priority`, `tags`, `startedAt`, `updatedAt`, `closedAt` e `metrics`.  
- **Chamadas:** representam registros individuais dentro de um atendimento, contendo atributos como `id`, `atendimentoId`, `topic`, `startedAt`, `endedAt`, `messages`, `meta` e `stars`.  

&emsp;Os relacionamentos são definidos da seguinte forma: um **Cliente** pode estar associado a diversos **Atendimentos** (1:N), um **Atendente** pode conduzir diversos **Atendimentos** (1:N), e cada **Atendimento** é composto por múltiplas **Chamadas** (1:N).  


## 4.3 Modelo Lógico de Dados

<div align="center">
  <sub>Figura X - Modelo Lógico</sub><br>
  <img src="../documentos/img/modelo_logico.png" alt="Modelo Conceitual" width="100%"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp;O modelo é centrado em Atendimento, que conecta Cliente e Atendente e agrega as Chamadas realizadas naquele caso.

### Entidades e chaves

- **Cliente (__id, name, role_)**: identifica quem abriu o caso.

- **Atendente (__id, name, role_):** identifica quem prestou o atendimento.

- **Atendimentos (__id, clienteId, atendenteId, subject, status, priority, tags, startedAt, updatedAt, closedAt, metrics_ ):** é o “ticket” em si. Guarda estado, datas e um resumo para listagem.

- **Chamadas (__id, atendimentoId, topic, startedAt, endedAt, messages, meta, stars_):** cada ligação/contato pertencente ao atendimento. Texto e metadados vivem aqui.

### Relacionamentos

- Cliente 1 — N Atendimentos (via atendimentos.clienteId)
- Atendente 1 — N Atendimentos (via atendimentos.atendenteId)
- Atendimento 1 — N Chamadas (via chamadas.atendimentoId)

### Decisões de modelagem (MongoDB)

- **Referência no filho:** a relação é sempre do filho para o pai (…Id).
- **Mensagens embutidas na Chamada:** facilita ler a conversa completa sem “join”.
- **Resumo no Atendimento:** campos de status/metricas existem para listar rápido sem carregar todas as chamadas.

### Uso típico (1–2 queries)

- Listar atendimentos recentes por cliente/atendente a partir dos campos do próprio Atendimento.

- Ao abrir o detalhe, buscar as Chamadas por atendimentoId em ordem temporal.

- Esse desenho mantém os acessos principais simples, controle de crescimento por documento e evolução de schema sem quebra.

## 4.4 Modelo Físico de Dados

_conteúdo_

**Nota:** Insira uma explicação e direcionamento para o readme.md da pasta database.

# 5. Solução Técnica (Design)

## 5.1 Diagrama de Classes

&emsp; O diagrama de classes define a estrutura orientada a objetos do sistema, incluindo entidades como Cliente, Atendimento, Chamada, Nota e serviços como NLP e Insight, fornecendo a base para implementação do código e comunicação entre componentes.

### Visão Geral do Diagrama de Classes

&emsp;O diagrama de classes representa o modelo de domínio do sistema, identificando as principais entidades, seus atributos, métodos e relacionamentos. Ele foi desenvolvido com foco na avaliação de atendimento ao cliente, geração de notas automáticas e insights para analistas de qualidade.

As principais classes são:

- `Cliente` – representa o cliente que realiza atendimentos.
- `Atendente` – representa o profissional ou chatbot que realiza o atendimento.  
- `Analista` – usuário do sistema (analista de qualidade) responsável por acompanhar a avaliação dos atendimentos e verificar os insights gerados.
- `Atendimento` – core do sistema; engloba uma ou mais chamadas e é o objeto principal a ser avaliado.  
- `Chamada` – representa uma interação (WhatsApp, ligação, e-mail) dentro de um atendimento.  
- `Nota` – armazena a avaliação do atendimento, podendo ser gerada pelo NLP ou manualmente pelo analista.  
- `Roteiro` – contém os roteiros de atendimento que o NLP utiliza para comparar o que foi falado com o que deveria ser falado.  
- `NLP <<service>>` – responsável por processar as chamadas, transcritas ou não, e gerar notas automáticas.  
- `Insight <<service>>` – processa os dados do sistema para gerar gráficos e análises, auxiliando o analista.

<br>
<div align="center">
<sub>Figura 17 - Diagrama de Classes</sub>
<br>
<br>
<img src="./img/diagrama_de_classes.svg" alt='Imagem com o diagrama de classes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### Descrição das Classes

#### Classe `Cliente`

Representa um cliente do sistema que realiza atendimentos.

| Atributo  | Tipo  | Descrição                       |
| --------- | ----- | ------------------------------- |
| id        | UUID  | Identificador único do cliente  |
| nome      | String| Nome do cliente                 |
| cpf       | String| CPF do cliente                  |
| telefone  | String| Telefone de contato             |
| endereco  | String| Endereço do cliente             |

#### Classe `Atendente`

Representa o profissional (humano ou IA) que realiza as chamadas.

| Atributo  | Tipo           | Descrição                     |
| --------- | -------------- | ----------------------------- |
| id        | UUID           | Identificador único           |
| nome      | String         | Nome do atendente             |
| tipo      | TipoAtendente  | Humano ou IA                  |

#### Classe `Analista`

Representa o usuário do sistema responsável por avaliar atendimentos e gerar insights.

| Atributo  | Tipo  | Descrição                    |
| --------- | ----- | ---------------------------- |
| id        | UUID  | Identificador único          |
| nome      | String| Nome do analista             |

#### Classe `Atendimento`

Core do sistema; engloba múltiplas chamadas e recebe notas.

| Atributo    | Tipo       | Descrição                                |
| ----------- | ---------- | ---------------------------------------- |
| id          | UUID       | Identificador único do atendimento       |
| status      | Status     | Status atual do atendimento              |
| dataInicio  | DateTime   | Data de início do atendimento            |
| dataFim     | DateTime   | Data de término do atendimento           |
| severidade  | Severidade | Nível de severidade do atendimento       |

#### Classe `Chamada`

Representa cada interação dentro de um atendimento.

| Atributo | Tipo  | Descrição                       |
| -------- | ----- | ------------------------------- |
| id       | UUID  | Identificador único da chamada  |
| canal    | Canal | Canal de comunicação utilizado  |
| data     | DateTime | Data/hora da chamada          |
| ordem    | int   | Sequência da chamada dentro do atendimento |

#### Classe `Nota`

Armazena a avaliação de uma chamada ou atendimento.

| Atributo      | Tipo  | Descrição                                 |
| ------------- | ----- | ----------------------------------------- |
| id            | UUID  | Identificador único da nota               |
| cordialidade  | float | Avaliação da cordialidade                 |
| script        | float | Avaliação do cumprimento do roteiro       |
| tempoFalado   | float | Tempo total falado pelo atendente         |
| notaFinal     | float | Nota final calculada                      |

#### Classe `Roteiro`

Armazena os roteiros de atendimento para comparação pelo NLP.

| Atributo | Tipo | Descrição                |
| -------- | ---- | ------------------------ |
| id       | UUID | Identificador do roteiro |
| tema     | Tema | Tema do roteiro          |
| conteúdo | String | Texto do roteiro        |

#### Classe `NLP <<service>>`

Responsável pelo processamento das chamadas e geração de notas automáticas.

| Atributo    | Tipo         | Descrição                       |
| ----------- | ------------ | ------------------------------- |
| parametros  | List<String> | Parâmetros de configuração do NLP|

**Métodos:**
- `STT()`: converte áudio em texto (Speech-to-Text)  
- `NLU()`: interpreta o conteúdo da chamada e gera notas  

#### Classe `Insight <<service>>`

Processa dados do sistema para gerar gráficos e análises, auxiliando o analista.

**Métodos:**
- `avaliaAtendimentos()`: analisa dados de atendimentos e notas de um determinado atendente para gerar métricas.
- `melhorias()`: analisa dados de atendimentos, notas, atendentes e clientes para gerar relatórios e métricas

---

### Enums

- `TipoAtendente` – Humano ou IA  
- `Canal` – WhatsApp, Ligacao, e-mail  
- `Tema` – Conta, VazamentoDeGas, FaltaDeGas, SolicitacaoLigacao, Desligacao  
- `Status` – Resolvido, NaoResolvido, EmAndamento  
- `Severidade` – Critica, Alta, Media, Baixa  

---

### Relacionamentos

| Origem     | Destino     | Tipo de Relação | Descrição                                                  |
| ---------- | ---------  | --------------- | ---------------------------------------------------------- |
| Cliente    | Atendimento| 1:N             | Um cliente pode realizar vários atendimentos               |
| Atendimento| Chamada    | 1:N             | Um atendimento possui múltiplas chamadas                  |
| Atendente  | Chamada    | 1:N             | Um atendente pode atender várias chamadas                 |
| Chamada    | Nota       | 0..1            | Cada chamada pode receber uma nota                         |
| NLP        | Nota       | 1:N             | NLP gera notas para as chamadas                             |
| Roteiro    | Tema       | *:1             | Roteiro referencia um tema específico                      |
| Insight    | Atendimento| 0..1            | Insight pode gerar análises sobre um atendimento          |
| Insight    | Atendente  | 0..1            | Insight pode gerar análises sobre um atendente            |
| Insight    | Cliente    | 0..1            | Insight pode gerar análises sobre um cliente              |
| Analista   | Insight    | ..>             | Analista usa o serviço de geração de insights             |
| Analista   | Nota       | ..>             | Analista verifica/edita notas manualmente                 |

---

### Conclusão

&emsp; O diagrama de classes estruturou de forma clara os elementos centrais do sistema de avaliação de atendimentos, incluindo clientes, atendentes, analistas, chamadas, notas e serviços de NLP e Insight.  

&emsp; Os relacionamentos refletem corretamente a multiplicidade e o fluxo de responsabilidades, proporcionando uma base sólida para a implementação, avaliação automática e manual de atendimentos, bem como geração de insights e relatórios analíticos.

## 5.1.1 Evolução do Diagrama de Classes da UML

O diagrama de classes abaixo, representa a estrutura completa do sistema de automação para monitoramento de qualidade de atendimento telefônico da Comgás.


<br>
<div align="center">
<sub>Figura X - Diagrama de Classes</sub>
<br>
<br>
<img src="./img/diagramadeclasses.png" alt='Imagem com o diagrama de classes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### Principais Classes
As classes centrais do sistema, que representam os principais atores e a essência do negócio, são:

- **`Cliente`**: O cliente da Comgás que interage com o sistema.
- **`Atendente`**: O profissional (humano ou IA) que realiza o atendimento.
- **`Analista`**: O usuário do sistema responsável por gerenciar e revisar as avaliações de qualidade.
- **`Chamada`**: O núcleo do sistema, que representa cada interação de atendimento.
- **`AvaliacaoQualidade`**: O resultado principal do sistema, que armazena as notas e métricas geradas.
---

### Estrutura Organizacional
O sistema é organizado em camadas para facilitar a manutenção e a escalabilidade.

#### Camadas Arquiteturais
- **Camada de Serviços:** Classes responsáveis pelo processamento e gerenciamento de dados.
- **Camada de ML:** Módulos especializados em aprendizado de máquina e análise de qualidade.
- **Entidades de Domínio:** Classes que representam os conceitos centrais do negócio.
- **Enumerações:** Tipos de dados controlados e categorizados.

---

### Classes de Serviço (Camada de Serviços)

#### **ServicoUpload**
Gerencia o processo de upload e registro de chamadas telefônicas no sistema. Recebe arquivos de áudio, valida a integridade, o formato e cria os registros de chamada no banco de dados.

- **Métodos:** `registrarChamada()`, `validarAudio()`

#### **ServicoTranscricao**
Converte áudio em texto utilizando tecnologia *Speech-to-Text*. Processa os arquivos de áudio para gerar transcrições, calcula a confiança, detecta o idioma e gerencia filas de processamento assíncrono.

- **Métodos:** `transcrever()`, `obterStatus()`

#### **ServicoConsulta**
Fornece uma interface para consulta e visualização de resultados de avaliações. Processa consultas de dados, agrega métricas para painéis (*dashboards*) e gera relatórios em diferentes formatos.

- **Métodos:** `buscarResultados()`, `gerarDashboard()`, `exportarRelatorio()`



### Classes de ML (Camada de Machine Learning)

#### **ModuloTreinamentoModelo**
Gerencia o ciclo de vida de treinamento e o versionamento de modelos de ML. Treina novos modelos, valida a performance com conjuntos de teste e ativa uma versão específica do modelo para produção.

- **Métodos:** `treinarModelo()`, `validarModelo()`, `ativarModelo()`

#### **ModuloAvaliacaoQualidade**
Aplica os modelos treinados para avaliar a qualidade das chamadas. Executa uma avaliação completa, gerando métricas objetivas como análise de sentimento e aderência a roteiros, e calcula a nota final para o atendimento.

- **Métodos:** `avaliarChamada()`, `analisarSentimento()`, `compararScript()`
---


### Entidades de Domínio

#### **Cliente**
Representa os clientes da Comgás que realizam chamadas.
- **Atributos:** `id: UUID`, `nome: String`, `cpf: String`, `telefone: String`

#### **Atendente**
Representa os profissionais (humanos ou IA) que atendem as chamadas.
- **Atributos:** `id: UUID`, `nome: String`, `tipo: TipoAtendente`

#### **Analista**
Representa os usuários do sistema responsáveis por análises e revisões.
- **Atributos:** `id: UUID`, `nome: String`, `setor: String`

#### **Chamada**
O núcleo do sistema, representa cada interação de atendimento.
- **Atributos:** `id: UUID`, `canal: Canal`, `dataHora: DateTime`, `duracao: Duration`, `status: EstadoProcessamento`

#### **ArquivoAudio**
Representa o arquivo de áudio original da chamada.
- **Atributos:** `id: UUID`, `caminhoArquivo: String`, `duracao: Duration`, `formato: String`, `tamanhoMB: Float`

#### **Transcricao**
Armazena o resultado da conversão de áudio para texto.
- **Atributos:** `id: UUID`, `textoCompleto: String`, `confianca: Float`, `idioma: String`, `geradaEm: DateTime`

#### **AvaliacaoQualidade**
Armazena as métricas e scores de qualidade das chamadas.
- **Atributos:** `id: UUID`, `tipoAvaliacao: TipoAvaliacao`, `notaCordialidade: Float`, `notaScript: Float`, `notaSentimento: Float`, `notaFinal: Float`, `observacoes: String`, `criadaEm: DateTime`

#### **ModeloVersao**
Representa as versões dos modelos de ML utilizados para avaliação.
- **Atributos:** `id: UUID`, `nomeModelo: String`, `versao: String`, `acuracia: Float`, `ativo: Boolean`, `criadoEm: DateTime`

#### **Roteiro**
Armazena os roteiros padrão para comparação com as chamadas.
- **Atributos:** `id: UUID`, `titulo: String`, `tema: Tema`, `conteudo: String`, `versao: String`

---

### Enumerações (Enums)

- **TipoAtendente:** `HUMANO`, `IA`
- **Canal:** `WHATSAPP`, `LIGACAO`, `EMAIL`
- **Tema:** `CONTA`, `VAZAMENTO_GAS`, `FALTA_GAS`, `SOLICITACAO_LIGACAO`
- **TipoAvaliacao:** `AUTOMATICA`, `MANUAL`
- **EstadoProcessamento:** `PENDENTE`, `TRANSCRITO`, `AVALIADO`, `ERRO`

---

## Relacionamentos

| Origem | Destino | Tipo de Relação | Descrição |
| --- | --- | --- | --- |
| Cliente | Chamada | `1:0..*` | Um cliente pode realizar várias chamadas. |
| Atendente | Chamada | `1:0..*` | Um atendente pode atender várias chamadas. |
| Chamada | ArquivoAudio | `1:1` | Cada chamada possui um único arquivo de áudio. |
| Chamada | Transcricao | `1:0..1` | Cada chamada pode ter, no máximo, uma transcrição. |
| Chamada | AvaliacaoQualidade | `1:0..*` | Uma chamada pode receber múltiplas avaliações (automática e manuais). |
| AvaliacaoQualidade | ModeloVersao | `0..*:1` | Várias avaliações automáticas são geradas por um único modelo. |
| AvaliacaoQualidade | Roteiro | `0..*:0..1` | Uma avaliação pode ser baseada em um roteiro, e um roteiro pode ser usado em várias avaliações. |
| AvaliacaoQualidade| Analista | `0..*:0..1` | Uma avaliação pode ser revisada por um analista. |
| Roteiro | Tema | `1:1` | Cada roteiro aborda um tema específico. |
| Analista | ServicoConsulta | `1:1` | Um analista utiliza o serviço de consulta. |

---

## Fluxos de Processo

1.  **Fluxo de Processamento:** O `ServicoUpload` registra a `Chamada` e o `ArquivoAudio`. O `ServicoTranscricao` converte o áudio em `Transcricao`. Em seguida, o `ModuloAvaliacaoQualidade` cria a `AvaliacaoQualidade`. Por fim, o `Analista` usa o `ServicoConsulta` para visualizar os resultados.
2.  **Fluxo de Treinamento de ML:** O sistema coleta dados de chamadas e avaliações. O `ModuloTreinamentoModelo` treina e valida uma nova `ModeloVersao`, que é ativada para ser usada em novas avaliações.
3.  **Fluxo de Revisão Manual:** Um `Analista` pode revisar a `AvaliacaoQualidade` automática, criando uma nova avaliação manual, que serve para refinar o modelo de ML.


## 5.2 Diagrama de Componentes da UML

<div align="center">
<sub>Figura 17 - Esboço do Diagrama de Componentes</sub><br>
<br>
<img src="./img/diagrama_de_componentes.png" alt='Imagem com o diagrama de classes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>


<div align="center">
<sub>Figura 17 - Diagrama de Componentes UML</sub><br>
<br>
<img src="./img/diagrama_de_componentes_uml.png" alt='Imagem com o diagrama de componentes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>


### Interface
&emsp;Responsável pela interação com o usuário. Permite envio de áudios, visualização de notas, ajustes manuais e consulta de métricas e insights.

### API Gateway
&emsp;Ponto central de entrada para todas as requisições da Interface. Encaminha chamadas para os módulos corretos, gerencia autenticação e autorização.

### Módulo Consultas
&emsp;Responsável por consultas simples no banco de dados, como buscar notas automáticas, filtrar chamadas por intenção, severidade e período. Também é responsável por enviar as notas dadas manualmente. Opera diretamente sobre o DB SQL.

### Módulo Insights
&emsp;Processa dados para análises avançadas e geração de métricas inteligentes, possibilitando a geração de possíveis insights. Sua carga de processamento é pesada, por isso é um módulo separado. 

### Módulo NLP
&emsp;Serviço que processa as chamadas e gera notas automáticas. Funciona em conjunto com o DB SQL e NoSQL, realizando:
- Transcrição de áudio (Speech-to-Text);
- Análise de conteúdo da chamada (NLU) + avaliação da ligação;
- Inserção de notas automáticas e metadados no DB SQL e NoSQL.

### Módulo Upload
&emsp;Gerencia o upload de áudios e textos. Encaminha os áudios para o NLP para transcrição e armazena textos/transcrições no DB NoSQL diretamente.

### DB SQL
&emsp;Armazena dados estruturados do sistema, como atendimentos, notas, usuários, auditoria e classificações. É utilizado principalmente pelo Módulo Consultas e Insights.

### DB NoSQL
&emsp;Armazena dados semi-estruturados ou grandes volumes de informação, como transcrições completas, eventos e textos processados. É utilizado pelo NLP e pelo Upload.

## Fluxos principais
1. **Consulta de notas:** Interface → API Gateway → Módulo Consultas → DB SQL
2. **Edição de notas / auditoria:** Interface → API Gateway → Módulo Insights → DB SQL / NLP
3. **Processamento de chamadas:** Interface (Upload) → API Gateway → NLP → DB SQL / NoSQL
4. **Upload de áudio:** Interface → API Gateway → Módulo Upload → NLP / DB NoSQL

## 5.2.1 Evolução do Diagrama de Componentes da UML

O diagrama de componentes é um artefato da UML utilizado para representar a organização e as dependências entre os blocos de software de um sistema. Ele mostra os principais módulos (componentes), suas responsabilidades e como eles interagem entre si.
<div align="center">
<sub>Figura X - Diagrama de Componentes UML</sub><br>
<br>
<img src="./img/diagramcomponent.png" alt='Imagem com o diagrama de componentes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### Camadas e Componentes

#### 1. Camada de Apresentação
- **Interface Web**: responsável pela interação com o usuário final. É o ponto de entrada para envio de chamadas (áudios) e consultas de resultados. Essa camada centraliza a usabilidade e envia as requisições para a camada de serviços.

#### 2. Camada de Serviços
- **API Gateway**: atua como ponto central de entrada para todas as requisições vindas da Interface Web, garantindo roteamento adequado, segurança e controle de acesso.
- **Upload de Chamadas**: recebe os arquivos de áudio enviados pelos usuários e os encaminha para transcrição.

- **Serviço de Consulta**: permite que o usuário acesse resultados de análises de qualidade já realizadas, diretamente do banco de dados.

#### 3. Camada de ML
- **Serviço de Transcrição (STT)**: converte os áudios recebidos em texto utilizando técnicas de *speech-to-text*. O texto resultante é armazenado no banco de dados para o modelo usar depois.
- **Módulo de Treinamento de Modelo**: utiliza os dados armazenados no banco (chamadas transcritas) para treinar modelos de *machine learning* que suportam a avaliação automática de qualidade. avaliação automática de qualidade.
- **Módulo de Avaliação de Qualidade**: aplica o modelo treinado para avaliar a qualidade das chamadas, gerando métricas objetivas e análise de sentimento. Os resultados são gravados no banco e disponibilizados para consulta.

#### 4. Camada de Armazenamento
- **BD NoSQL**: repositório central que armazena chamadas (transcrições), resultados de análises, métricas de qualidade e versões do modelo de *machine learning*. Sua escolha é motivada pela flexibilidade em lidar com dados semiestruturados.



## Fluxos Representados pelas Setas:

- `Interface Web` <-> `API Gateway`: O usuário interage com o sistema pela interface, que envia as requisições para o gateway. Depois as respostas dessas requisiçoes voltam via API Gateway.
- `API Gateway` -> `Upload de Chamadas`: As chamadas de áudio são direcionadas ao serviço responsável por recebê-las.
- `Upload de Chamadas` -> `Serviço de Transcrição (STT)`: Os áudios recebidos são encaminhados para o serviço de transcrição.
- `Serviço de Transcrição (STT)` -> `BD NoSQL`: O resultado da transcrição (texto) é armazenado no banco de dados.
- `API Gateway` <-> `Serviço de Consulta` <-> `BD NoSQL`: O usuário solicita relatórios/resultados, e o serviço de consulta acessa o banco para trazer os dados processados e volta para a interface via API Gateway.
- `Módulo de Treinamento de Modelo` <-> `BD NoSQL`: O módulo acessa o banco para extrair dados de treinamento e pode salvar versões de modelos.
- `Módulo de Treinamento de Modelo` -> `Módulo de Avaliação de Qualidade`: O modelo treinado é disponibilizado ao módulo de avaliação.
- `Módulo de Avaliação de Qualidade` -> `BD NoSQL`: O módulo lê dados para análise e grava as métricas geradas no banco.




### Complementos Importantes
- O **API Gateway** é fundamental para desacoplar a apresentação da lógica de negócios, facilitando escalabilidade e segurança.
- A **Camada de ML** é modular, permitindo evolução de modelos sem impacto direto nos serviços.
- O uso de **BD NoSQL** facilita armazenar dados em formatos variados (áudio, texto, métricas estruturadas).
- O fluxo é cíclico: novas chamadas enriquecem a base, que retroalimenta o treinamento do modelo, melhorando continuamente a qualidade da avaliação.

## 5.3 Diagrama de Sequência de Casos Críticos da UML


O digrama abaixo representa os 4 cenários críticos do sistema de automação de monitoramento de qualidade de atendimento telefônico da Comgás. Cada caso representa uma situação específica que o sistema deve tratar de forma certa e confiável.

<div align="center">
<sub>Figura X - Diagrama de Sequencia de Casos Críticos UML</sub><br>
<br>
<img src="./img/diagramasequenciacasoscriticos.png" alt='Imagem com o diagrama de componentes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>




### Componentes do Sistema

#### Atores e Participantes

| Componente | Tipo | Responsabilidade |
|------------|------|------------------|
| **Usuario** | Ator | Usuário final que interage com o sistema (analistas, supervisores) |
| **InterfaceWeb** | Camada de Apresentação | Interface gráfica para interação com usuários |
| **API Gateway** | Camada de Serviços | Ponto central de entrada, roteamento e controle de acesso |
| **ServicoUpload** | Camada de Serviços | Gerencia upload e validação inicial de arquivos de áudio |
| **ServicoTranscricao** | Camada de Serviços | Converte áudio em texto utilizando tecnologia Speech-to-Text |
| **ModuloAvaliacaoQualidade** | Camada de ML | Aplica modelos de análise e gera scores de qualidade |
| **ServicoConsulta** | Camada de Serviços | Gerencia consultas, relatórios e alertas do sistema |
| **BD_NoSQL** | Camada de Armazenamento | Base de dados para armazenamento de dados semi-estruturados |



### Descrição dos Casos Críticos

#### Caso Crítico 1: Processamento Completo de Ligação

**Objetivo:** Fluxo principal do sistema, desde o upload até a geração do score final.

##### Fluxo Detalhado:

1. **Entrada do Usuário:** O usuário faz upload de um arquivo de áudio através da interface web
2. **Validação Inicial:** O sistema valida formato e qualidade do áudio
3. **Transcrição:** Serviço converte áudio em texto utilizando tecnologia de Speech-to-Text
4. **Armazenamento:** As Transcrições são salvas no banco de dados
5. **Análise de Qualidade:** Modelos de machine learning analisam o texto transcrito
6. **Geração de Score:** Sistema calcula pontuação de 0 a 5 baseada em critérios objetivos
7. **Persistência:** Resultados são salvos no banco de dados
8. **Retorno:** Score final é apresentado ao usuário

#### Pontos Críticos:

- Validação rigorosa de qualidade do áudio
- Aplicação consistente de modelos de análise
- Feedback imediato ao usuário

---

#### Caso Crítico 2: Detecção de Emergência (Vazamento)

**Objetivo:** Identificação automática de situações de risco para classificação como chamada de emergência.

##### Fluxo Detalhado:

1. **Análise de Conteúdo:** Sistema analisa texto transcrito em busca de palavras-chave críticas
2. **Detecção de Padrões:** Identifica menções a "vazamento", "cheiro de gás" ou termos similares
3. **Classificação de Emergência:** Classifica como chamada de emergência
4. **Registro de Emergência:** Salva no banco como chamada de emergência
5. **Consulta Rápida:** Possibilita consulta rápida às chamadas de emergência
6. **Exibição:** Chamadas de emergência são apresentadas na interface do usuário

#### Pontos Críticos:

- Detecção precisa de situações de emergência
- Classificação adequada de chamadas críticas
- Registro de auditoria para rastreabilidade
- Facilidade de consulta a emergências

---

#### Caso Crítico 3: Falha de Transcrição

**Objetivo:** Tratamento de situações onde áudio com muito ruído/sotaque impede transcrição adequada.

##### Fluxo Detalhado:

1. **Processamento de Áudio:** Áudio com muito ruído ou sotaque é processado
2. **Qualidade Degradada:** Qualidade da transcrição fica comprometida
3. **Falha no Speech-to-Text:** Sistema identifica falha na conversão
4. **Tratamento de Erro:** Sistema aplica rotina de tratamento de erro
5. **Escalação Manual:** O sistema não salva o áudio nem transcrição; retorna mensagem de erro e sugere análise manual
6. **Comunicação ao Usuário:** Interface exibe mensagem "Não foi possível transcrever o áudio, sugerimos fazer análise manual"

#### Pontos Críticos:

- Identificação adequada de falhas de transcrição
- Comunicação clara sobre o processo manual

---

#### Caso Crítico 4: Comparação Humano vs IA

**Objetivo:** Processo de melhoria contínua comparando avaliações automatizadas com humanas.

##### Fluxo Detalhado:

1. **Coleta de Dados:** Sistema salva score gerado automaticamente pela IA
2. **Busca de Referência:** Recupera avaliação humana correspondente do banco
3. **Cálculo de Diferença:** Compara resultados usando fórmula (humano - IA)
4. **Armazenamento de Calibração:** Salva dados estatísticos para análise
5. **Ajuste de Parâmetros:** Sistema recebe informações para ajuste de modelos
6. **Retreinamento:** Modelos são refinados baseado nas diferenças identificadas
7. **Atualização de Métricas:** Sistema registra melhoria na acurácia

#### Pontos Críticos:

- Comparação matemática precisa entre scores
- Rastreabilidade das melhorias implementadas
- Ciclo contínuo de aperfeiçoamento
- Monitoramento da evolução da acurácia

### Conclusão

Este diagrama representa os cenários mais críticos do sistema de monitoramento de qualidade, cobrindo desde operações normais até situações excepcionais.

## 5.4 Visão Inicial sobre Tecnologias e Ferramentas 
&emsp; Para o desenvolvimento do projeto, a equipe optou por ferramentas robustas e adequadas ao contexto. As tecnologias escolhidas foram selecionadas estrategicamente para garantir uma solução automatizada eficiente no monitoramento da qualidade do atendimento telefônico.

**Principais Tecnologias Utilizadas:**

- **Reconhecimento de fala:** O [Whisper](https://openai.com/pt-BR/index/whisper/) (OpenAI) será usado para converter áudio em texto com alta precisão, uma etapa fundamental para a análise automatizada das chamadas.
- **Análise de sentimentos e scoring:** A biblioteca Scikit-learn será utilizada para gerar uma pontuação por ligação, um dos principais objetivos do MVP.
- **Visualização:** O Streamlit (Python) ou React servirá para criar uma interface simples, como um dashboard com gráficos, para exibir os resultados da análise.
- **Pré-processamento:** O Pandas será usado para otimizar a preparação dos dados.
- **Linguagem:** Python com FastAPI para desenvolver a API REST que integrará os módulos e gerenciará requisições.
- **Banco de dados**: Banco de dados não relacional para armazenar os dados fornecidos
- **Virtualização:** Docker para containerizar a aplicação, garantindo portabilidade e deploy consistente em diferentes ambientes
- **Processamento de linguagem natural:** NLP será aplicado para extrair dados qualitativos e quantitativos dos textos.

## 5.5 Escolha do algoritmo de Processamento de Linguagem Natural

&emsp;Observando os materiais da empresa parceira, tais como checklists de qualidade e as próprias transcrições de chamadas, percebe-se algumas atividades principais que serão performadas pelos algoritmos de processamento escolhidos: a identificação do atendente por meio de fraseologias obrigatórias da Comgás; a solicitação de dados como nome completo, CPF, código de usuário, endereço, telefone, e-mail e RG; o registro de protocolos e a menção de números específicos; o uso de frases padrão tanto no início quanto no encerramento da ligação; a detecção de linguagem imprópria no texto, incluindo gírias, palavrões, gerundismo (por exemplo, “vou estar transferindo”) e uso incorreto de pronomes (como “para mim fazer”); a confirmação de informações obrigatórias em notas e cadastros; a oferta de serviços adicionais, como débito automático, conta por e-mail e Comgás Virtual; a empatia e cordialidade do atendente; e, por fim, a avaliação da clareza e completude textual em campos de anotações. 

&emsp;A equipe orientou sua pesquisa para minimizar custo computacional e maximizar interpretabilidade, sobretudo neste MVP. Por isso, selecionaram-se ferramentas que cobrem a maior parte do escopo: *regex* e dicionários, classificadores leves (*TF‑IDF + Logistic Regression*)e *BERTimbau* quando a análise exige contexto (empatia, ironia, ato de fala sutil). Complementos como *sentence‑transformers* (similaridade semântica) entram onde há variação de fraseologias quase fixas. Para determinadas atividades, como o cálculo do tempo de resposta do atendente, sobreposição de fala e tempos de silêncio, pode-se utilizar operações com **python**, pois possuímos acesso ao timestamp (marcação de tempo) de cada fala durante uma conversa.

### Escolhas de algoritmos por categoria de análise

- **Abertura/Encerramento e Pausa:**
  - *timestamp*: avaliar tempo de resposta, interrupções e janelas de espera;
  - *regex* + dicionários de palavras-chave: identificações de fraseologias padrão da Comgás (ex: *"Olá, sou Júlia, da Comgás..."*);
  - Sentence embeddings para detectar pedidos de contato em caso de queda da ligação e retorno, mesmo em casos de variações frasais;
  - TF-IDF + LogReg: detecção de momentos de hold/retorno do atendente;

- **Condução do Atendimento**:
  - BERTimbau: detecção de empatia/tonalidade na fala/cordialidade;
  - regex: solicitação de dados sensíveis como CPF ou códigos;
  - TF-IDF + LogReg: detecção de sondagem, perguntas exploratórias, indícios de interrupção.

- **Processos e Procedimentos**:
  - regex: detecção de confirmação de e-mail, telefone, RG, protocolo;
  - TF-IDF + BERTimbau: avaliação de coerência de respostas do atendente com o histórico do atendimento;
  - TF-IDF + LogReg: detecção de intenção comercial do atendente, como a oferta de produtos da Comgás.

- **NCG/Falhas Graves**:
  - BERTimbau: detecção de toxicidade/ironia;

- **Diagnóstico de Resolução:**
  - regex + embeddings: detecção de encerramento positivo e resoluções indiretas.

## Exemplos de Implementação

&emsp;**Identificação como empresa — regex + dicionário (fraseologia Comgás)**

&emsp;O output será True se uma mensagem mencionar "Congás" ou "companhia de gás" (considerando variações acentuadas e de grafia), e False caso contrário. Este cenário serve para a conferência do critério de qualidade no qual o atendente precisa se apresentar como representante da Comgás.
```python
import re
KW = [r"\bcon?g[áa]s\b", r"\bsou\s+.*\s+da\s+con?g[áa]s\b", r"\bcompanhia de g[áa]s\b"]
def citou_empresa(texto: str) -> bool:
    t = texto.lower()
    return any(re.search(pat, t) for pat in KW)

# Exemplo 1: Deve retornar True
print(citou_empresa("Olá! Sou a Juliana, da Comgás."))  
# Saída: True

# Exemplo 2: Deve retornar False
print(citou_empresa("Olá! Sou Maria, sua atendente hoje."))  
# Saída: False

```

&emsp;**Pedido de contato em caso de queda — sentence embeddings**
```python
from sentence_transformers import SentenceTransformer, util
import numpy as np

sbert = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")
templates = [
    "no caso de queda de ligação, posso retornar neste número?",
    "se a chamada cair, posso ligar de volta neste número?",
    "se cair a ligação, posso retornar para este telefone?"
]
emb_temp = sbert.encode(templates, normalize_embeddings=True)

def fraseologia_queda(sentenca: str, limiar: float = 0.72) -> bool:
    emb = sbert.encode([sentenca], normalize_embeddings=True)
    sims = util.cos_sim(emb, emb_temp).cpu().numpy()[0]
    return float(np.max(sims)) >= limiar
```

&emsp;**Detecção de hold/retorno — TF‑IDF + LogReg**
```python
from sklearn.pipeline import Pipeline
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from datetime import timedelta

# treinamento ilustrativo
frases = [
  "um instante por favor, vou verificar",  # HOLD
  "aguarde na linha por gentileza",        # HOLD
  "obrigado por aguardar, vamos continuar",# RETORNO
  "retornando com as informações"          # RETORNO
]
y = ["HOLD", "HOLD", "RETORNO", "RETORNO"]

detector_hold = Pipeline([
    ("tfidf", TfidfVectorizer(ngram_range=(1,2))),
    ("clf", LogisticRegression(max_iter=1000))
]).fit(frases, y)

def retorno_em_ate_1_min(ts_hold: str, ts_retorno: str) -> bool:
    t0 = datetime.fromisoformat(ts_hold)
    t1 = datetime.fromisoformat(ts_retorno)
    return (t1 - t0) <= timedelta(seconds=60)
```

---

### 2) Condução do Atendimento

&emsp;**Empatia/Tonalidade — BERTimbau**
```python
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

MODEL_ID = "neuralmind/bert-base-portuguese-cased"   # BERTimbau base
tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
model = AutoModelForSequenceClassification.from_pretrained(MODEL_ID, num_labels=2)  # 0=NAO, 1=EMPATICO

frase = "Entendo a sua preocupação, Camila. Vou ajudar."
inputs = tokenizer(frase, return_tensors="pt", truncation=True, padding=True)
with torch.no_grad():
    probs = torch.softmax(model(**inputs).logits, dim=-1)[0].tolist()
prob_empatia = probs[1]
```

&emsp;**Solicitação de CPF/código — regex**
```python
import re
CPF = re.compile(r"\b\d{3}\.\d{3}\.\d{3}-\d{2}\b|\b\d{11}\b")
COD_CLIENTE = re.compile(r"\b(c[oó]d(igo)?|id)\b.*\b\d{4,}\b", flags=re.I)

def solicitou_cpf_ou_codigo(texto: str) -> bool:
    t = texto.lower()
    # presença do termo + (opcionalmente) padrão numérico; ajustável conforme roteiro
    return "cpf" in t or bool(CPF.search(t)) or bool(COD_CLIENTE.search(t))
```

&emsp;**Sondagem/Perguntas exploratórias — TF‑IDF + LogReg**
```python
from sklearn.pipeline import Pipeline
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression

# 1 = sondagem; 0 = pergunta operacional simples
frases = ["poderia detalhar o que ocorreu?", "me conte melhor o que aconteceu", "qual é o endereço completo?"]
y = [1, 1, 0]

clf_sondagem = Pipeline([
    ("tfidf", TfidfVectorizer()),
    ("clf", LogisticRegression(max_iter=1000))
]).fit(frases, y)
```

&emsp;**Indícios de interrupção — TF‑IDF + LogReg**
```python
from sklearn.pipeline import Pipeline
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression

# 1 = indício de interrupção; 0 = neutro
exemplos = ["deixa eu te interromper um instante", "só um segundo, vou falar agora", "entendi, pode continuar"]
y_int = [1, 1, 0]

clf_interrupcao = Pipeline([
    ("tfidf", TfidfVectorizer(ngram_range=(1,2))),
    ("clf", LogisticRegression(max_iter=1000))
]).fit(exemplos, y_int)
```

---

### 3) Processos e Procedimentos

&emsp;**Regex para e‑mail, telefone, RG, protocolo**
```python
import re
EMAIL = re.compile(r"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}")
TELEFONE_BR = re.compile(r"\(?\d{2}\)?\s?9?\d{4}-?\d{4}")
RG_BR = re.compile(r"\b\d{1,2}\.?\d{3}\.?\d{3}-?[\dxX]\b")         # padrões comuns de RG
PROTOCOLO = re.compile(r"\b\d{6,}-\d{3}\b")

def extrair_contatos_e_protocolo(texto: str):
    return {
        "emails": EMAIL.findall(texto),
        "telefones": TELEFONE_BR.findall(texto),
        "rgs": RG_BR.findall(texto),
        "protocolos": PROTOCOLO.findall(texto),
    }
```

&emsp;**Intenção comercial (oferta/canais digitais) — TF‑IDF + LogReg**
```python
from sklearn.pipeline import Pipeline
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression

frases = ["posso ativar fatura digital", "já conhece nosso app", "posso enviar a segunda via por e-mail?"]
y = [1, 1, 1]  # 1 = intenção comercial (exemplo; incluir negativos reais no treino)

clf_comercial = Pipeline([
    ("tfidf", TfidfVectorizer(ngram_range=(1,2))),
    ("clf", LogisticRegression(max_iter=1000))
]).fit(frases, y)
```

---

### 4) NCG/Falhas Graves

&emsp;**Toxicidade/Ironia — BERTimbau**
```python
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

MODEL_ID = "neuralmind/bert-base-portuguese-cased"
tok = AutoTokenizer.from_pretrained(MODEL_ID)
tox_model = AutoModelForSequenceClassification.from_pretrained(MODEL_ID, num_labels=2)  # 0=NAO,1=TOX

texto = "isso é um absurdo, vocês nunca resolvem nada"
with torch.no_grad():
    p = torch.softmax(tox_model(**tok(texto, return_tensors="pt", truncation=True, padding=True)).logits, dim=-1)[0,1].item()
prob_tox = p
```

---

### 5) Diagnóstico de Resolução

&emsp;**Encerramento positivo — regex + embeddings**
```python
from sentence_transformers import SentenceTransformer, util
import re, numpy as np

MARCADORES_OK = [r"\bresolvido\b", r"\bconclu[ií]do\b", r"\bprotocolo\b", r"\bos\s+\d+"]
def encerramento_regex(texto: str) -> bool:
    t = texto.lower()
    return any(re.search(pat, t) for pat in MARCADORES_OK)

sbert = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")
templates_ok = ["o problema foi resolvido", "seu pedido está concluído", "serviço restabelecido"]
emb_ok = sbert.encode(templates_ok, normalize_embeddings=True)

def encerramento_embeddings(sentenca: str, limiar: float = 0.70) -> bool:
    emb = sbert.encode([sentenca], normalize_embeddings=True)
    sims = util.cos_sim(emb, emb_ok).cpu().numpy()[0]
    return float(np.max(sims)) >= limiar
```

## Agregação para Nota (1 a 5)

&emsp;Cada ponto do checklist produz um **sinal binário** (ok/falha). A nota final do atendimento seria o resultado de uma **média ponderada** por categoria.

```python
PESOS = {"abertura": 1.0, "conducao": 2.0, "processos": 1.5, "ncg": 3.0, "resolucao": 2.0}
def nota_final(scores: dict) -> float:
    num = sum(scores[c]*PESOS[c] for c in scores)
    den = sum(PESOS.values())
    return round(1 + 4*(num/den), 2)  # normaliza 0..1 -> 1..5
```

## 5.6 Pilha de Tecnologias para Implementação da Solução

&emsp; A escolha das tecnologias que compõem o projeto foi guiada pelas demandas arquitetônicas do sistema, que exige [modularidade, portabilidade e capacidade de processamento de linguagem natural (PLN)](#33-requisitos-não-funcionais-rnfs). Aqui são apresentadas as decisões sobre a plataforma de execução, linguagens e frameworks, garantindo uma implementação de qualidade.

- **Visualização (Streamlit)**: Fornece uma interface rápida e interativa para dashboard e gráficos, especialmente para exibição de resultados.
- **Pré-processamento (Pandas)**: Biblioteca principal para manipulação e limpeza de dados, garantindo qualidade de dados para o modelo.
- **Linguagem (Python)**: Linguagem principal para desenvolvimento do modelo de ML, APIs e integração entre todos os componentes do sistema.
- **Virtualização (Docker)**: Serve para facilitar a conteinerização da aplicação para garantir ambiente consistente, portátil e escalável desde o desenvolvimento até a produção.
- **APIs (FastAPI)**: Framework que será utilizado para criar APIs rápidas e documentadas automaticamente, facilitando a integração e o consumo dos serviços.
- **Plataforma de Execução (Web)**: A solução será desenvolvida como uma aplicação web, garantindo acesso via navegador, sem a necessidade de instalação local.
- **Disponibilização (Cloud)**: O deploy será feito em um provedor cloud (como AWS, Google Cloud ou Azure), assegurando escalabilidade, alta disponibilidade e segurança para a aplicação.
- **Algoritmos para Abertura/Encerramento e Pausa:**  
  - **timestamp**: Análise de tempos de resposta e períodos de espera ou interrupção;  
  - **regex + dicionários de palavras-chave:** Identificação de fraseologias padrão da Comgás (ex: *"Olá, sou Júlia, da Comgás..."*);  
  - **sentence embeddings**: Detecção de pedidos de retorno de contato em caso de queda da ligação, mesmo com variações frasais;  
  - **TF-IDF + Regressão Logística (LogReg):** Detecção de momentos de espera (hold) e retorno do atendente.  
- **Algoritmos para Condução do Atendimento:**  
  - **BERTimbau:** Detecção de empatia, tonalidade e cordialidade na fala;  
  - **regex:** Identificação de solicitações de dados sensíveis (ex: CPF, códigos);  
  - **TF-IDF + LogReg:** Detecção de sondagem, perguntas exploratórias e sinais de interrupção.  
- **Processos e Procedimentos:**  
  - **regex:** Detecção de confirmação de dados (e-mail, telefone, RG, protocolo);  
  - **TF-IDF + BERTimbau:** Avaliação da coerência entre respostas do atendente e o histórico do atendimento;  
  - **TF-IDF + LogReg:** Detecção de intenções comerciais, como oferta de produtos da Comgás.  
- **NCG/Falhas Graves:**  
  - **BERTimbau (com Fine-tuning):** Detecção de toxicidade e ironia. O modelo BERTimbau base não é um classificador pronto, ele exige um processo de fine-tuning com um dataset específico para ensinar o modelo a realizar essas tarefas.

# 6. Mapeamento Técnico de Infraestrutura e Implantação

## 6.1 Diagrama de Implantação da UML

<div align="center">
<sub>Figura 6.1.1 - Diagrama de Implantação UML</sub><br>
<br>
<img src="./img/diagrama_implantacao.svg" alt='Imagem com o diagrama de componentes' width="100%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### 6.1.1 Infraestrutura

#### 6.1.1.1 **Servidores e Recursos de Computação**
- **Interface Web (Servidor em Sub-rede Pública):**
  - **Tecnologia:** Instância EC2 da AWS em sub-rede pública para hospedagem da interface web construída em Streamlit (Python).
  - **Função:** O _Front-end_ disponibiliza a interface de interação com o usuário.
  - **Escalabilidade:** Pode ser configurado um _Auto Scaling Group_ para aumentar ou diminuir a capacidade de acordo com a demanda.
  - **Rede:** Localizado em sub-rede pública, acessível via HTTPS, sem dependência da VPC privada.

- **Serviços de Backend (Serverless - AWS Lambda):**
  - **Tecnologia:** Funções Lambda da AWS para os serviços de Upload de Chamadas e Consulta.
  - **Função:** Executam sob demanda as lógicas de negócio da aplicação, comunicando-se diretamente com o DynamoDB e com os módulos de ML no SageMaker.
  - **Escalabilidade:** As funções Lambda escalam automaticamente de acordo com o volume de requisições, sem necessidade de manutenção de servidores.
  - **Segurança:** Cada Lambda possui permissões mínimas configuradas por meio de **IAM Roles**, garantindo isolamento e segurança no acesso aos demais recursos.

#### 6.1.1.2 **Topologia de Rede e Comunicação entre os Componentes**

A **topologia de rede** e a **comunicação entre os componentes** seguem uma arquitetura **serverless** distribuída, com um ponto de entrada público (API Gateway), interface web em sub-rede pública e recursos de backend protegidos em uma VPC privada.

- **Cliente (Interface Web - EC2) → API Gateway:**
  - O **usuário** interage com a interface web hospedada em uma instância EC2 pública.
  - A interface se comunica com o **API Gateway** via **HTTPS/TLS**, garantindo criptografia dos dados em trânsito.

- **API Gateway → Serviços Backend (Lambda):**
  - O **API Gateway** roteia as requisições para as funções Lambda responsáveis pelos serviços de Upload e Consulta.
  - A comunicação é realizada via HTTPS com integração nativa ao API Gateway.

- **Upload de Chamadas (Lambda) → Bucket de Áudios (S3):**
  - O serviço de Upload envia os arquivos de áudio para um **bucket S3**.
  - Os áudios são armazenados temporariamente para posterior processamento pelo SageMaker.

- **Serviço de Transcrição (SageMaker Container) → Bucket de Áudios (S3):**
  - O container de STT no SageMaker consome os áudios diretamente do **S3**, realiza a transcrição e envia o resultado ao DynamoDB.
  - Após a transcrição, os arquivos de áudio são removidos do bucket, garantindo que não permaneçam armazenados.

- **Serviços Backend (Lambda) → Banco de Dados (DynamoDB):**
  - As funções Lambda interagem com o banco de dados **DynamoDB** via SDK da AWS, utilizando permissões restritas do IAM.
  - A comunicação é criptografada e ocorre dentro da infraestrutura AWS.

- **Serviços Backend (Lambda) → Módulos de Machine Learning (SageMaker):**
  - As funções Lambda também se comunicam com o **SageMaker** para enviar dados de treinamento e solicitar inferências (avaliações de qualidade).
  - A comunicação é feita via SDK/API da AWS, utilizando autenticação IAM.

#### ******* **Banco de Dados**
- **DynamoDB (Banco de Dados NoSQL):**
  - **Tecnologia:** AWS DynamoDB.
  - **Função:** Armazenamento e gerenciamento dos dados transacionais e resultados das análises.
  - **Escalabilidade:** Escala horizontal automaticamente, suportando grandes volumes de leitura e escrita sob demanda.
  - **Segurança:** O banco de dados está isolado dentro da VPC e acessível apenas por recursos autorizados (Lambdas e SageMaker), garantindo uma camada adicional de segurança.

#### ******* **Gerenciamento de Permissões e Autenticação**
- **AWS Cognito (Gerenciador de Permissões):**
  - **Tecnologia:** AWS Cognito para controle de autenticação de usuários e gerenciamento de permissões com JWT.
  - **Função:** Autenticação via OAuth2, gerenciando o acesso dos usuários ao sistema e integrando-se com o API Gateway.
  - **Segurança:** Utilização de tokens **JWT (JSON Web Token)** para garantir a autenticidade e autorização de usuários.

#### 6.1.1.5 **Serviços de Machine Learning**
- **AWS SageMaker (Transcrição, Treinamento e Avaliação de Modelos):**
  - **Tecnologia:** AWS SageMaker.
  - **Função:** 
    - Um **container de STT** roda no SageMaker, responsável por consumir os áudios armazenados no S3 e gerar as transcrições.
    - Além disso, o SageMaker realiza o treinamento e avaliação dos modelos de Machine Learning responsáveis pela análise de qualidade e insights do sistema.
  - **Integração:** 
    - O container de STT interage diretamente com o **S3** e o **DynamoDB**.
    - Os módulos de treinamento e avaliação são consumidos pelas funções Lambda através de chamadas autenticadas ao serviço do SageMaker.
  - **Segurança:** As permissões são controladas por políticas do IAM, permitindo apenas acesso autorizado.

## 6.2 Justificativa das Escolhas de Implantação

&emsp;As escolhas de implantação em nuvem foram definidas de forma a atender os requisitos não funcionais de **manutenibilidade**, **portabilidade**, **desempenho/eficiência** e **usabilidade**, sempre equilibrando **segurança, escalabilidade e custos**.

##### **RNF01 - Arquitetura Modular**
- **Justificativa**: A utilização de **AWS Lambda** para os serviços de Upload, Consulta e Transcrição, junto ao **API Gateway** como ponto de entrada, promove uma arquitetura modular e desacoplada. Isso facilita substituições e atualizações de componentes individuais sem impactar o restante do sistema, reforçando a **manutenibilidade**.

##### **RNF04/RNF12 - Execução Local e Portabilidade via Docker**
- **Justificativa**: A opção por empacotamento em **Docker** e compatibilidade com execução local garante que o mesmo código possa rodar tanto no ambiente de nuvem (AWS) quanto localmente. Essa abordagem aumenta a **portabilidade**, reduz erros de configuração e melhora a produtividade no desenvolvimento.

##### **RNF07 - Confiabilidade através de Testes e Monitoramento**
- **Justificativa**: A infraestrutura em AWS permite integração direta com **CloudWatch** para logs e métricas, aumentando a observabilidade. Em conjunto com cobertura mínima de testes automatizados (80% para código crítico), isso assegura a **confiabilidade** do sistema.

##### **RNF09/RNF11 - Usabilidade e Feedback Imediato**
- **Justificativa**: O uso do **Streamlit em EC2** para frontend garante uma interface simples e acessível. Em paralelo, o processamento no backend via Lambda permite que mensagens de status sejam exibidas durante as análises, melhorando a experiência do usuário e atendendo requisitos de **usabilidade**.

##### **RNF15 - Tempo de Resposta**
- **Justificativa**: O processamento em **Lambda** reduz latência inicial, já que as funções são executadas sob demanda, e o **DynamoDB** fornece baixa latência (<10ms) em consultas. Isso assegura que o score automático de qualidade esteja disponível em até 4 segundos após o carregamento da página.

---

### 6.2.1 Escalabilidade e Custos

- **Escalabilidade Automática (Serverless):**
  - Funções **Lambda** escalam automaticamente de acordo com o número de requisições, sem necessidade de configurar servidores adicionais.
  - O **API Gateway** distribui as chamadas, suportando milhares de requisições por segundo de forma elástica.
  - O **DynamoDB** escala horizontalmente conforme a demanda, evitando gargalos em leitura/escrita.

- **Otimização de Custos:**
  - **Lambda** adota o modelo de pagamento por uso (pay-per-request), ideal para sistemas que podem ter variação de tráfego, como é o caso dos serviços que utilizam _lambda_.
  - **EC2** pode ser configurada em instâncias de baixo custo (ex.: t3.medium) para o frontend Streamlit, com possibilidade de Auto Scaling se necessário.
  - O **DynamoDB** utiliza cobrança por leitura/escrita sob demanda, reduzindo custos em períodos de baixa utilização.
  - **SageMaker** permite treinar modelos em instâncias otimizadas apenas quando necessário, desligando-as após uso e evitando cobrança contínua.

- **Benefício do Modelo Híbrido (EC2 + Serverless):**
  - O **frontend** em EC2 garante disponibilidade contínua da interface.
  - O **backend** em **Lambda** elimina custo ocioso, já que os serviços são executados apenas quando chamados.
  - Essa combinação assegura **eficiência de custo** sem comprometer **desempenho** ou **escalabilidade**.



## 6.3 Considerações sobre Desempenho e Segurança

&emsp;As escolhas de tecnologias e infraestrutura em nuvem foram realizadas com foco em garantir **desempenho, escalabilidade e segurança** em conformidade com os requisitos não funcionais definidos.

### 6.3.1 Desempenho

- **Escalabilidade Automática:**  
  - O uso de **AWS Lambda** permite que os serviços backend (Upload, Consulta e Transcrição) escalem automaticamente de acordo com a demanda, eliminando a necessidade de configuração manual de servidores adicionais.  
  - O **API Gateway** lida com grandes volumes de requisições simultâneas, distribuindo as chamadas para as Lambdas sem comprometer a performance.  
  - O **DynamoDB** oferece escalabilidade horizontal sob demanda, garantindo baixa latência de consulta e escrita (<10ms).  

- **Frontend com EC2:**  
  - A interface em **Streamlit (EC2)** garante disponibilidade contínua para o usuário, com possibilidade de Auto Scaling em caso de aumento de tráfego.  

- **Processamento de Machine Learning:**  
  - O **SageMaker** é utilizado para treinar e avaliar modelos sob demanda, evitando sobrecarga em tempo de execução.  
  - Essa separação permite que a execução dos modelos seja otimizada em instâncias específicas e desligadas após o uso, garantindo eficiência de recursos.  

- **Tempo de Resposta:**  
  - A combinação de **Lambda + DynamoDB** assegura que as consultas de qualidade sejam respondidas em até 4 segundos (RNF15), atendendo ao requisito de desempenho.  

---

### 6.3.2 Segurança

- **Isolamento de Infraestrutura com VPC:**  
  - A aplicação está hospedada em uma **VPC (Virtual Private Cloud)**, que provê isolamento dos recursos críticos.  
  - A **EC2** (frontend) fica em sub-rede pública, enquanto Lambdas, DynamoDB e SageMaker operam integrados à VPC com controle de acesso restrito.

- **Autenticação e Autorização Segura:**  
  - O **AWS Cognito** gerencia a autenticação de usuários, utilizando **OAuth2** e tokens **JWT** para validação.  
  - Esse modelo garante que apenas usuários autenticados possam acessar a API via API Gateway.  

- **Proteção de Comunicações:**  
  - Todas as comunicações entre cliente, API Gateway e serviços backend são realizadas via **HTTPS/TLS**, garantindo confidencialidade e integridade dos dados.  
  - O DynamoDB utiliza criptografia em trânsito (TLS) e em repouso com **AWS KMS**.  

- **Controle de Acesso Granular:**  
  - Cada função **Lambda** possui permissões mínimas definidas via **IAM Role**, reduzindo superfícies de ataque.  
  - O princípio de **least privilege** é aplicado em todos os componentes.  

- **Proteção contra Ameaças Externas:**  
  - O **API Gateway** pode ser integrado ao **AWS WAF** para mitigar ataques como SQL Injection, XSS e DDoS.  
  - **Rate limiting e throttling** são configurados no API Gateway para evitar abuso de requisições.  

- **Monitoramento e Auditoria:**  
  - O **CloudWatch** centraliza logs e métricas das aplicações, permitindo rastreabilidade de erros e análise de performance.  
  - O **CloudTrail** mantém trilhas de auditoria das chamadas feitas na AWS, garantindo rastreabilidade administrativa.  
  - Serviços como **GuardDuty** podem ser habilitados para detecção de comportamento anômalo.  

# 7. Projeto Visual da Solução

## 7.1 Desenvolvimento de Wireframes

&emsp;Os wireframes de baixa fidelidade representam a arquitetura de informação, fluxos principais e padrões de interação da solução antes da definição visual. Eles guiam decisões de navegação e priorização de conteúdo para que o mockup e o desenvolvimento avancem com menos retrabalho. Nesta fase, usamos tons neutros e blocos (cards/faixas) apenas para comunicar hierarquia e comportamento, não estilo final.

&emsp;Consolidamos hipóteses a partir de: entrevistas e observações com perfis-alvo (analista de qualidade, gestor de CX, supervisor de operações e TI/governança), análise de jornadas típicas e restrições de compliance/LGPD. Principais achados que orientaram os wireframes:

- **Velocidade para achar desvios**: usuários querem ver “o que mais importa” em 10–15 segundos (alertas, piores notas, outliers).
- **Drill-down sem fricção**: de um indicador agregado para a lista filtrada e, em dois cliques, para o detalhe do atendimento e suas chamadas.
- **Acessibilidade e aprendizagem rápida**: layout previsível, ações claras, foco em teclado/leitores de tela e erros/estados vazios prototipados.

> Nota: por se tratar de um projeto acadêmico, parte das necessidades acima são hipóteses de trabalho e serão validadas/usabilidade no MVP.

### 7.1.1 Wireframe — Dashboard

<div align="center">
<sub>Figura X – Wireframe Dashboard</sub><br>
<img src="./img/Dashboard.svg" alt="Imagem contendo o wireframe da tela de dashboard da solução" width="100%">
<br><sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp;A Dashboard foi voltada principalmente para a persona relacionada com Costumer Experience, interessada em retirar insights a partir das métricas sobre os atendimentos, como o número de atendimentos por classificação de notas, desempenho anual, principais atividades detectadas pelos atendentes, etc.

&emsp;Tem-se o menu lateral do lado esquerdo, pelo qual será possível ter acesso às demais páginas. No menu superior, tem-se o botão de acesso ao menu lateral, a logo no centro e a foto de perfil do usuário na direita.

&emsp;No decorrer da dashboard, tem-se gráficos e cards de métricas relacionadas ao atendimento, os quais, quando clicados, levam à página de Listas de Transcrições.

### 7.1.2 Wireframe — Listas de Atendimentos

<div align="center">
<sub>Figura X – Wireframe Listas de Atendimentos</sub><br>
<img src="./img/projeto-visual/wireframeAtendimentos.svg" alt="Imagem contendo o wireframe da tela de listas de atendimentos da solução" width="100%">
<br><sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp;A lista de Transcrições busca permitir busca, filtragem e priorização rápida dos atendimentos que precisam de revisão, tendo-se um botão no canto superior direito para a adição de um novo atendimento, filtros de busca logo abaixo, bem como a lista de atendimentos em si, possibilitnado uma visão geral das informações.

### 7.1.3 Wireframe — Detalhes de um Atendimento

<div align="center">
<sub>Figura X – Wireframe Detalhes de um Atendimento</sub><br>
<img src="./img/projeto-visual/wireframeDetalhes.svg" alt="Imagem contendo o wireframe da tela de detalhes de um atendimento da solução" width="100%">
<br><sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&emsp;Essa página de detalhes do atendimento centraliza todas as informações relacionadas a um chamado específico, permitindo que o usuário acompanhe de forma clara o andamento do caso. Nela estão visíveis os dados principais do atendimento (como status, prioridade e responsável) e o histórico de interações já realizadas. Além disso, são apresentadas tags classificativas, que resumem a intenção do atendimento e trazem insights.

## 7.2 Desenvolvimento de Mockups

&emsp; Esta documentação descreve a interface de usuário (UI) do _mockup_ para o sistema de monitoramento de qualidade de atendimento da Comgás. O desenvolvimento da interface foi realizado utilizando a ferramenta Figma, link para acesso ao protótipo pode ser encontrado [aqui](https://www.figma.com/design/rh4h4oiWDJVooPyt511CLj/Carbon?node-id=0-1&t=cIOD6MtbtmH6wMU2-1). A interface é composta por três telas principais: o Dashboard, a Listagem de Transcrições e a Tela de Detalhes da Ligação. A escolha de cores e elementos visuais foi planejada para garantir acessibilidade, considerando especialmente usuários que possam ter deficiências visuais, como baixa visão.

&emsp;Esse é um processo de amadurecimento de mapeamento de ideias, de modo que entende-se a possibilidade de mudança ao longo do desenvolvimento, seja na organização ou estruturação de algum item.

### Dashboard

&emsp;O Dashboard é a tela inicial e fornece uma visão geral das métricas de atendimento. O objetivo da elaboração dessa tela é exercer pensamento crítico para mapear as principais KPIs do cliente e entender como fazer bom uso do espaço para apresentar esses dados.   

<div align="center">
<sub>Figura X - Mockup Dashboard</sub>  
<img src="./img/projeto-visual/dashboard.svg " alt="Imagem contendo o mockup do dashboard da solução" width="100%"> 
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**KPIs (Cards de Métricas):** No topo da tela, cards exibem os principais indicadores de desempenho, como o número total de avaliações e a distribuição de notas (zero, uma, duas, três, quatro e cinco estrelas).

**Gráfico de Linha:** O gráfico "Chamada por mês" mostra a tendência de chamadas positivas, neutras e negativas ao longo do tempo.

**Gráfico de Barras Empilhadas:** O gráfico "Chamada por semana" mostra a distribuição de chamadas positivas, neutras e negativas por trimestre. A hierarquia de cores (verde, roxo e laranja) facilita a identificação rápida do desempenho da qualidade.

**Painel de Avaliações:** O painel "Avaliações" na lateral direita fornece uma legenda visual para as notas de atendimento, de "Péssima" a "Ótima".

### Listagem de Transcrições 

Esta tela permite a busca e a filtragem de todas as ligações avaliadas.

<div align="center">
<sub>Figura X - Mockup Listagem das Transcrições</sub>
<img src="./img/projeto-visual/transcricoes.svg" alt="Imagem do mockup da tela de listagem das transcrições" width="100%">
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Filtros:** A barra superior contém filtros para buscar por categoria ("Todas", "Negativas", "Positivas", "Neutras"), assunto e ID da chamada. Essa funcionalidade é essencial para o fluxo de trabalho do usuário.

**Tabela de Dados:** A tabela exibe uma lista de atendimentos, com colunas para ID, Protocolo, Avaliação, Assunto, Atendente e Data. A última coluna com o ícone de + sugere que é possível expandir os detalhes daquela linha.

**Paginação:** A paginação na parte inferior da tabela indica que o sistema pode lidar com um grande volume de dados.

### Detalhes do Atendimento

Esta é a tela que exibe a transcrição completa de uma ligação e seus detalhes.

<div align="center">
<sub>Figura X - Mockup dos Detalhes das Transcrições</sub>
<img src="./img/projeto-visual/detalhes.svg" alt="Imagem contendo o mockup da tela de detalhes sobre as transcrições." width="100%">  
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Breadcrumbs:** No topo da tela, os breadcrumbs mostram a rota e direcionam para voltar para a tela anterior, melhorando a usabilidade.

**Painel de Detalhes:** A área central da tela exibe informações como ID, Protocolo, Avaliação, Assunto, Data e Atendente.

**Transcrição da Chamada:** A área principal mostra a transcrição completa da chamada. Embora o texto seja lorem ipsum no mockup, a intenção é que aqui seja exibido o texto da transcrição. 

## 7.3 Guia Visual

&emsp;O Guia Visual foi elaborado em paralelo ao desenvolvimento dos **mockups de interface** e busca garantir **consistência estética, identidade coesa e escalabilidade do design** durante todas as iterações. O sistema visual define as bases para cores, tipografia e componentes reutilizáveis, assegurando clareza e facilidade de manutenção.

&emsp;O ponto de partida foi a **identidade visual proposta pelo grupo**, que equilibra tons institucionais (verdes, cinzas) com cores de apoio vibrantes (roxo, amarelo e vermelho). Essa combinação busca transmitir seriedade, inovação e acessibilidade, mantendo o contraste adequado para leitura.

&emsp;Para os componentes, optamos por um conjunto essencial que reflete a **estrutura mínima de navegação** (logo, navbar e footer), compondo a base para páginas simples e escaláveis.

### Paleta de Cores

| Cor              | Código Hex | Aplicação                                    |
| ---------------- | ---------- | -------------------------------------------- |
| Verde primário   | `#00B259`  | Cor principal (identidade, botões primários) |
| Roxo primário    | `#6B1D91`  | Cor de destaque e identidade secundária      |
| Preto intenso    | `#1F2124`  | Header, rodapé e textos de alta hierarquia   |
| Cinza médio      | `#999999`  | Textos secundários e bordas                  |
| Cinza claro      | `#D2D6DB`  | Fundo de cartões e seções de apoio           |
| Branco           | `#FFFFFF`  | Fundo principal                              |
| Vermelho alerta  | `#D92D20`  | Mensagens de erro/alerta                     |
| Amarelo destaque | `#FEC84B`  | Notificações e avisos                        |
| Verde sucesso    | `#4ADE80`  | Mensagens de confirmação                     |

### Tipografia

- **Fonte:** Roboto  
- **Estilo principal: **Tamanho mínimo para Body Text – 16px  

### Componentes Reutilizáveis

- **Logo**: Versão circular em verde, com ícone centralizado, aplicada em fundo escuro para contraste.  
- **Navbar**: Barra de navegação simples, com logo ao centro e itens principais distribuídos horizontalmente.  
- **Footer**: Rodapé com logotipo alinhado à esquerda e links institucionais à direita.  

<div align="center">
<sub>Figura X - Guia Visual</sub><br> 
<img src="img/guiaVisual.png" width="70%"><br> 
<sup>Fonte: Material produzido pelos autores (2025) </sup>
</div>


# 8. Desenvolvimento do Projeto

_conteúdo_

## 8.1 Arquitetura de Codificação e Estrutura de Diretórios

_conteúdo_

## 8.2 Modelo de Recomendação

_conteúdo_

## 8.3 Desenvolvimento de Features


### - Estratégia de Entrega para Sprints 3, 4 e 5

Definição da Estratégia de Entrega

&emsp;Com base na análise das escolhas tecnológicas documentadas e no entendimento dos requisitos do projeto, foi elaborada uma estratégia de entrega progressiva para as Sprints 3, 4 e 5, focando no desenvolvimento incremental do backend e sua integração com os componentes de frontend. A estratégia segue uma abordagem metodológica que prioriza a entrega de valor ao usuário final através de funcionalidades testáveis e integradas.

&emsp;A estratégia de desenvolvimento será estruturada em ciclos iterativos, onde cada sprint adiciona camadas de funcionalidade ao sistema, começando pela infraestrutura base e evoluindo até funcionalidades avançadas de análise e visualização.

### - Sprint 3

### Fundamentos e Infraestrutura do Sistema

**Objetivo:** Estabelecer a base técnica e os componentes fundamentais do sistema de análise de qualidade de atendimento.


**Desenvolvimento Backend:**
- **API de Upload e Processamento de Áudio:**
  - Implementação da API REST usando FastAPI para recebimento de arquivos de áudio
  - Validação de formatos suportados (.wav, .mp3)
  - Sistema de filas assíncronas para processamento
  - Integração com modelo Whisper local (execução offline) para Speech-to-Text
    - Download e versionamento dos artefatos do modelo em repositório de modelos interno
    - Suporte a modelos quantizados (int8/float16) para reduzir uso de memória quando necessário
    - Configuração opcional para acelerar inferência via GPU (CUDA/cuDNN) quando disponível
    - Fallback para processamento em CPU em lote/filas com throttling quando não houver aceleração por GPU
  - Armazenamento de transcrições com timestamps

  - **Subtarefas operacionais importantes (para integração com o board / frontend):**
    - Criar webhooks ou endpoints-stub para upload e callbacks de processamento (facilitar paralelismo com frontend)
    - Documentar esquema de payloads (OpenAPI) para upload, status e callbacks
    - Script de seed/população do banco local para testes e demonstrações (CSV/JSON fixtures)

- **Infraestrutura Base:**
  - Containerização completa com Docker
  - Configuração do banco de dados NoSQL para armazenamento de transcrições
  - Sistema de logs estruturados
  - Monitoramento básico (healthchecks, métricas CPU/mem, logs centralizados em ambiente de dev)
  - Configuração de ambiente de desenvolvimento local

- **Processamento NLP Básico:**
  - Implementação do pipeline spaCy com modelo pt_core_news_lg
  - Tokenização e análise morfossintática básica
  - Estruturas de dados para armazenamento de análises

**Testes:**
- Testes unitários para APIs de upload
- Testes de integração com o modelo Whisper local (validação de acurácia e latência por variantes de modelo)
- Testes de desempenho em configurações CPU-only e GPU (quando disponível)
- Testes de integração para cenários de fila/overflow (verificar fallback em modo batch)
- Validação de containerização, incluindo imagens que contenham os binários/weights necessários ou mecanismos de pull seguro dos modelos

**Observações sobre escopo de deploy (Sprint 3):**
- A equipe elaborou um diagrama de organizaçao e ordenamento de tarefas que inclui tarefas de infraestrutura cloud (EC2, S3, IAM, DynamoDB, SageMaker, Lambdas). Essas tarefas devem permanecer no backlog ou serem remarcadas para Sprint 4/5 salvo decisão explícita do time de executar deploy remoto ainda na Sprint 3. A Sprint 3 prioriza ambiente local e integração frontend/backend via webhooks/stubs.

**Implantação:**
- Deploy em ambiente de desenvolvimento (local)

**Entregáveis:**
- Sistema de upload e transcrição funcionando
- Documentação técnica inicial

### - Sprint 4

### Core de Análise e Scoring

**Objetivo:** Implementar o núcleo de análise de qualidade com algoritmos de NLP e sistema de scoring.

**Design:**
- Refinamento da interface baseado em feedback da Sprint 3
- Implementação visual dos componentes de análise

**Desenvolvimento Backend:**
- **Sistema de Análise de Qualidade:**
  - Análise de cordialidade usando classificadores spaCy
  - Verificação de aderência a scripts com Matcher e PhraseMatcher
  - Análise de tempo de fala e proporção cliente/atendente
  - Detecção de interrupções e sobreposições de voz
  - Algoritmo de scoring (0-5) baseado nos critérios da Comgás

- **Processamento NLP Avançado:**
  - Implementação de EntityRuler para entidades específicas (protocolo, CPF, etc.)
  - Classificadores customizados para identificação de intenções
  - Análise de sentimentos complementar usando ferramentas auxiliares
  - Validação de dados (CPF, telefone, e-mail) usando regex e bibliotecas especializadas

- **API de Consulta e Relatórios:**
  - Endpoints para recuperação de análises
  - Sistema de filtros por data, agente, tipo de atendimento
  - Agregação de métricas e KPIs

**Subtarefas operacionais sugeridas (incluir no backlog da Sprint 4):**
- Atualizar documentação OpenAPI / descrição dos endpoints após integração (upload, status, consulta)
- Criar scripts/rotinas para popular o DB em ambiente de homologação (fixtures e migrations)
- Automatizar processos de migração/população para facilitar homologação e testes de carga
- Checklist de integração frontend/backend por tela (concordar nº de telas: decidir 2 ou 3 e refletir no backlog)
- Plano de testes de usabilidade (ex.: 5 usuários, critérios de sucesso, roteiro rápido)

**Testes:**
- Testes de acurácia do modelo NLP (meta: 80%)
- Testes de performance do sistema de scoring
- Testes de integração entre componentes

**Observações sobre telas e UI:**
- O diagrama da equipe apresenta 3 telas funcionais; o documento planejava dashboard + filtros. Decidam e documentem se serão 2 ou 3 telas (impacta tasks de frontend e QA) e alinhem no backlog.

**Implantação:**
- Deploy em ambiente remoto
- Testes de carga iniciais

**Entregáveis:**
- Sistema de análise e scoring operacional
- APIs de consulta implementadas
- Métricas de acurácia validadas
- Infraestrutura containerizada

### Sprint 5

### Interface Avançada e Otimização

**Objetivo:** Finalizar o sistema com interface completa, otimizações e preparação para produção.

**Design:**
- Interface final do dashboard com visualizações interativas
- Tela de filtros com seletores dinâmicos para geração de insights
- Otimização de UX baseada em testes com usuários

**Desenvolvimento Backend:**
- **Dashboard Backend:**
  - APIs para alimentação de gráficos e métricas
  - Sistema de cache para otimização de performance
  - Agregações em tempo real de dados históricos
  - Exportação de relatórios em múltiplos formatos

- **Sistema de Insights:**
  - Identificação automática de padrões de atendimento
  - Detecção de erros recorrentes e lacunas de conhecimento
  - Correlações entre sentimento, score e resolução
  - Sugestões automáticas de melhoria

- **Otimizações e Refinamentos:**
  - Otimização de queries no banco NoSQL
  - Implementação de cache distribuído
  - Refinamento dos algoritmos de NLP baseado em feedback
  - Tratamento avançado de erros e casos extremos

**Desenvolvimento Frontend:**
- Interface web responsiva usando React ou Streamlit
- Visualizações interativas com bibliotecas de gráficos
- Sistema de notificações e alertas
- Interface de configuração de critérios de qualidade

**Testes:**
- Testes de aceitação com stakeholders
- Testes de performance e escalabilidade
- Cobertura de testes automatizados (meta: 80%)
- Testes de usabilidade (meta: primeira análise em 3 minutos)

**Implantação:**
- Deploy em ambiente de produção
- Configuração de monitoramento avançado com alertas
- Documentação completa para operação
- Treinamento da equipe de qualidade da Comgás

**Entregáveis:**
- Sistema completo em produção
- Dashboard funcional
- Documentação de usuário e operação
- Treinamento da equipe concluído

### Estratégia de Integração e Qualidade

**Integração Contínua:**
- Commits seguindo padrão Conventional Commits
- Pipeline automatizado de testes em cada pull request
- Deploy automatizado nos ambientes de desenvolvimento e homologação

**Controle de Qualidade:**
- Code reviews obrigatórios
- Métricas de qualidade de código
- Monitoramento contínuo de performance
- Feedback loops regulares com stakeholders

**Gestão de Riscos:**
- Testes com dados anonimizados da Comgás
- Planos de fallback para falhas ou indisponibilidade do modelo local: alternativa por VOSK ou modelos quantizados menores; execução em modo batch com enfileiramento quando recursos forem limitados
- Monitoramento proativo de uso de recursos (CPU/GPU/memória) e anomalias de inferência
- Documentação de procedimentos de contingência e instruções para atualização/roll-back de weights de modelo

Esta estratégia garante entregas incrementais de valor, permitindo validação contínua com os stakeholders e ajustes baseados em feedback real, otimizando o processo de desenvolvimento e assegurando a qualidade do sistema final.

## 8.3.1 Sprint 3

## 8.3.2 Sprint 4

## 8.3.3 Sprint 5

## 8.4 Testes Unitários e de Integração

_conteúdo_
Obs.: Insira informações sobre estratégias para realização dos testes

## 8.5 Documentações automáticas

_conteúdo_
Obs.: Insira informações sobre quais frameworks foram usados e como acessar.

# 9. Planejamento e Execução de Testes

&emsp;Esta seção apresenta o planejamento abrangente de testes funcionais e não funcionais para validar o comportamento esperado do sistema de automação de monitoramento de qualidade de ligações da Comgás. O objetivo é garantir que todos os requisitos de negócio e de projeto sejam atendidos integralmente, assegurando a qualidade, confiabilidade e usabilidade da solução.

## 9.1 Testes Funcionais

&emsp;Os testes funcionais verificam se o sistema executa corretamente as funções especificadas nos requisitos funcionais. Estes testes validam o comportamento do sistema em relação às entradas fornecidas, verificando se as saídas produzidas estão de acordo com o esperado.

## 9.1.1 Planejamento

&emsp;O planejamento dos testes funcionais baseia-se nos oito requisitos funcionais (RF01 a RF08) definidos para o sistema. Cada teste foi estruturado para validar cenários positivos e negativos.

### Ferramentas e Bibliotecas para Testes Funcionais

- **Pytest**: Framework principal para execução de testes automatizados em Python
- **Requests**: Biblioteca para testes de APIs REST (FastAPI)
- **Pandas**: Manipulação e validação de dados (já utilizado no projeto)
- **Docker**: Ambiente isolado para execução de testes (tecnologia do projeto)
- **Streamlit**: Testes de interface do dashboard (tecnologia principal do frontend)

### Tabela de Testes Funcionais

| **Cenário** | **RF Testado** | **Pré-condição** | **Pós-condição** | **Resultado Esperado** |
|-------------|------------|------------------|------------------|------------------------|
| **TF01 - Ingestão de Áudio Válido** | RF01 | Sistema inicializado; arquivo .wav/.mp3 válido disponível; API de upload ativa | Arquivo processado e armazenado; transcrição gerada com timestamps | Upload aceito com status 201; logs de processamento registrados |
| **TF02 - Ingestão de Formato Inválido** | RF01 | Sistema inicializado; arquivo em formato não suportado (.txt, .doc) | Erro retornado; arquivo rejeitado | Status 400 com mensagem clara de erro; arquivo não processado; log de erro registrado |
| **TF03 - Classificação Automática de Intenção** | RF02 | Transcrição processada; taxonomia de intenções configurada | Intenção primária e secundária atribuídas; etiquetas aplicadas | Chamada rotulada com intenção correta (vazamento, falta de gás, conta);  e filtros funcionais |
| **TF04 - Detecção de Status de Resolução** | RF03 | Transcrição com indicadores de resolução; regras configuradas | Status "resolvida" ou "não resolvida" atribuído | Campo status atribuído corretamente; KPIs calculados (% resolvida)|
| **TF05 - Cálculo de Score de Qualidade** | RF04 | Transcrição processada; checklist parametrizado; pesos definidos | Score 0-5 calculado com evidências | Score calculado corretamente|
| **TF06 - Score com Critérios Ausentes** | RF04 | Transcrição incompleta; alguns critérios não detectados | Score parcial calculado; critérios ausentes identificados | Score proporcional aos critérios detectados; relatório de critérios ausentes; evidências disponíveis para critérios detectados |
| **TF07 - Identificação de Orientações Incorretas** | RF05 | Ligação onde atendente deu informações erradas; roteiros de atendimento disponíveis | Erros de orientação identificados; trechos problemáticos destacados | Alerta "orientação incorreta" com trecho da conversa que comprova o erro|
| **TF08 - Dashboard Executivo** | RF06 | Pipeline de dados consolidado; permissões configuradas | Dashboard carregado com KPIs atualizados | Cards com KPIs atualizados; gráficos de barras/linha; exportação CSV disponível |
| **TF09 - Reprodução da Ligação com Marcações** | RF07 | Ligação analisada; texto da conversa e eventos importantes identificados | Reprodutor com pontos importantes marcados | Barra de tempo mostrando momentos relevantes; possibilidade de ajustar avaliações|
| **TF10 - Comparação entre Análise Automática e Manual** | RF08 | Grupo de ligações selecionadas; duas pessoas analisando manualmente | Relatório de precisão criado | Relatório mostrando acertos e erros do sistema comparado com análise humana; registro da versão do sistema testada |
| **TF11 - Processamento de Lote Grande** | RF01, RF02 | Sistema configurado; lote de 100+ ligações | Todas as ligações processadas sem erro | Processamento completo sem falhas; tempos de resposta dentro do limite; recursos do sistema estáveis |
| **TF12 - Envio de Arquivo de Áudio Danificado** | RF01 | Sistema funcionando; arquivo de áudio com problemas/defeitos | Problema identificado; arquivo recusado | Mensagem clara "arquivo danificado"; registro detalhado do erro; sistema continua funcionando normalmente |
| **TF13 - Dashboard com Falha de Conexão** | RF06 | Dashboard carregado; conexão com banco interrompida | Erro tratado graciosamente | Mensagem de erro clara; opção de recarregar; dados em cache exibidos se disponíveis |
| **TF14 - Transcrição com Áudio Inaudível** | RF01, RF04 | Sistema ativo; arquivo de áudio com muito ruído/inaudível | Transcrição parcial ou vazia gerada | Score baixo ou nulo; flag "qualidade de áudio insuficiente"; evidências limitadas documentadas |

### Abrangência dos Testes Funcionais

&emsp;Os testes funcionais planejados cobrem 100% dos requisitos funcionais definidos (RF01 a RF08), incluindo:

**Cenários Positivos (8 testes):**
- Ingestão e processamento correto de áudios válidos
- Classificação automática de intenções
- Cálculo de scores de qualidade com evidências
- Visualização de dashboards e relatórios
- Validação de acurácia do modelo

**Cenários Negativos (6 testes):**
- Tratamento de formatos de arquivo inválidos ou corrompidos
- Comportamento com dados ausentes ou incompletos
- Falhas de conexão e indisponibilidade de serviços
- Qualidade de áudio insuficiente para transcrição
- Detecção de erros de instrução e falhas de script



## 9.1.2 Resultados

_conteúdo_

## 9.2 Testes de RNFs

&emsp;Os testes de Requisitos Não Funcionais (RNFs) verificam as qualidades e características do sistema que não estão diretamente relacionadas às funcionalidades específicas, mas sim à forma como essas funcionalidades são entregues. Estes testes avaliam aspectos como desempenho, usabilidade, manutenibilidade, portabilidade, confiabilidade e segurança. 

## 9.2.1 Planejamento

&emsp;O planejamento dos testes de RNFs baseia-se nos quinze requisitos não funcionais (RNF01 a RNF15) definidos conforme a norma ISO/IEC 25010. Os testes incluem obrigatoriamente avaliações de desempenho, conforme especificado no artefato, além de testes de usabilidade, manutenibilidade, portabilidade e confiabilidade.

### Ferramentas e Bibliotecas para Testes de RNFs

- **Pytest**: Framework para testes automatizados (já utilizado no projeto)
- **Docker**: Testes de portabilidade e ambiente (tecnologia principal do projeto)
- **Locust**: Ferramenta para testes de carga e performance
- **Coverage.py**: Medição de cobertura de testes em Python
- **Streamlit**: Testes de interface e usabilidade (frontend do projeto)

### Tabela de Testes de RNFs

| **Cenário** | **RNF Testado** | **Pré-condição** | **Pós-condição** | **Resultado Esperado** |
|-------------|-------------|------------------|------------------|------------------------|
| **TNF01 - Teste de Carga de Upload** | RNF10, RNF15 | Sistema em produção; 50 usuários simultâneos; arquivos de áudio padrão | Sistema mantém responsividade; todas as requisições processadas | Tempo de resposta < 5 segundos por upload; taxa de sucesso > 95% |
| **TNF02 - Teste de Stress de Processamento** | RNF10, RNF15 | Sistema configurado; 100 ligações simultâneas para análise | Sistema processa sem falhas críticas | Processamento completo em < 10 minutos; logs de performance registrados |
| **TNF03 - Teste de Performance do Dashboard** | RNF10, RNF15 | Dashboard carregado; base com 1000+ registros | Interface responsiva; dados carregados | Carregamento inicial < 5 segundos; filtros aplicados < 2 segundos; gráficos renderizados < 3 segundos |
| **TNF04 - Teste de Acurácia do Modelo NLP** | RNF06 | Conjunto de teste validado; modelo treinado | Métricas de acurácia calculadas | Acurácia ≥ 80% no conjunto de testes; precisão e recall balanceados
| **TNF05 - Teste de Cobertura de Código** | RNF07 | Código completo; testes implementados | Relatório de cobertura gerado | Cobertura ≥ 80% para código crítico; relatório HTML gerado; áreas não cobertas identificadas |
| **TNF06 - Teste de Portabilidade Docker** | RNF04, RNF05, RNF12 | Dockerfile configurado; ambientes diferentes disponíveis | Aplicação executada consistentemente | Execução com `docker-compose up`; comportamento idêntico em Windows/Linux/Mac; logs sem erros |
| **TNF07 - Teste de Usabilidade - Primeiro Uso** | RNF09 | Sistema limpo; usuário sem treinamento | Primeira análise realizada | Usuário completa primeira análise em < 3 minutos; interface intuitiva; mensagens de ajuda claras |
| **TNF08 - Teste de Tratamento de Erros** | RNF08 | Sistema configurado; cenários de erro preparados | Erros tratados adequadamente | Mensagens claras; logs detalhados para debug; sistema mantém estabilidade |
| **TNF09 - Teste de Responsividade Visual** | RNF14 | Interface carregada; diferentes resoluções | Interface adaptada corretamente | Layout responsivo em desktop/tablet; elementos visíveis e funcionais; contraste adequado |
| **TNF10 - Teste de Tempo de Carregamento** | RNF10 | Máquina com configuração mínima; sistema inicializado | Sistema carregado dentro do prazo | Carregamento inicial < 5 segundos; componentes críticos priorizados; feedback de progresso exibido |
| **TNF11 - Teste de Mensagens de Status** | RNF11 | Processamento em andamento; interface ativa | Progresso comunicado ao usuário | Mensagens de status atualizadas; barra de progresso funcional |
| **TNF12 - Teste de Manutenibilidade** | RNF01, RNF02, RNF03 | Código versionado; padrões definidos | Código mantível e documentado | Commits seguem Conventional Commits; código formatado consistentemente; documentação atualizada |
| **TNF13 - Teste de Contraste e Acessibilidade** | RNF14 | Interface carregada; ferramentas de acessibilidade | Elementos visuais acessíveis | Tags e cores com contraste adequado; elementos focáveis; navegação por teclado funcional |
| **TNF14 - Teste de Performance de Score** | RNF15 | Transcrição carregada; modelo ativo | Score exibido rapidamente | Score exibido em < 4 segundos após carregamento|
| **TNF15 - Teste de Sobrecarga do Sistema** | RNF10 | Sistema em operação; carga excessiva aplicada (200+ usuários) | Sistema degrada graciosamente | Tempo de resposta aumenta mas é < 10 segundos; mensagens de "sistema ocupado"; sem crashes ou perda de dados |
| **TNF16 - Teste de Falha de Dependência** | RNF08, RNF12 | Sistema rodando; dependência externa indisponível | Erro tratado adequadamente | Mensagem clara de indisponibilidade; funcionalidades offline mantidas; logs de erro registrados |
| **TNF17 - Teste de Ambiente Incompatível** | RNF04, RNF12 | Docker não instalado; tentativa de execução | Erro detectado e comunicado | Mensagem clara sobre pré-requisitos; instruções de instalação; verificação de ambiente |

### Justificativa da Abrangência dos Testes de RNFs

&emsp;A abrangência dos testes de RNFs foi planejada para cobrir todos os aspectos críticos de qualidade do sistema:

**Cenários Positivos (14 testes):**
- Testes de performance dentro dos limites esperados
- Validação de usabilidade e acessibilidade
- Verificação de manutenibilidade e portabilidade
- Confirmação de acurácia e cobertura de código

**Cenários Negativos (3 testes):**
- Comportamento sob sobrecarga extrema do sistema
- Tratamento de falhas de dependências externas
- Detecção de ambientes incompatíveis

**Desempenho e Eficiência**
- Testes de carga simulam cenários reais com múltiplos usuários
- Testes de stress validam degradação graceful em condições extremas
- Medições de tempo de resposta garantem experiência adequada

**Qualidade e Confiabilidade:**
- Testes de acurácia do modelo NLP (≥80%)
- Cobertura de código crítico (≥80%)
- Tratamento adequado de erros e falhas

**Usabilidade e Acessibilidade:**
- Interface intuitiva para novos usuários
- Contraste e navegação acessíveis
- Responsividade em diferentes dispositivos

**Portabilidade e Manutenibilidade:**
- Execução consistente via Docker
- Padrões de código e versionamento
- Documentação e estrutura modular



## 9.2.2 Resultados

_conteúdo_

## 9.3 Testes de Usabilidade

&emsp;Os testes de usabilidade avaliam a facilidade de uso e satisfação dos usuários ao interagir com o sistema. Estes testes são fundamentais para garantir que a solução desenvolvida atenda às necessidades dos usuários finais, proporcionando uma experiência intuitiva. No contexto do projeto Carbon, os testes de usabilidade focam na validação da interface do dashboard, fluxos de análise de ligações e facilidade de interpretação dos resultados.

## 9.3.1 Planejamento

&emsp;O planejamento dos testes de usabilidade foi estruturado para envolver ao menos 5 usuários externos à turma. Os testes utilizarão a metodologia SUS (System Usability Scale) como métrica principal de avaliação, complementada por observação direta, análise de tarefas e coleta de feedback. 

### Ferramentas e Bibliotecas para Testes de Usabilidade

- **SUS (System Usability Scale)**: Questionário padronizado para avaliação de usabilidade
- **Pandas**: Análise quantitativa dos resultados (tecnologia do projeto)
- **Streamlit**: Interface de teste (tecnologia principal do frontend)

### Perfil dos Participantes

&emsp;Para garantir diversidade e representatividade, abaixo tem uma tabela com exemplos de perfis, que serve como guia para assegurar variação adequada de experiências e características:

| **Participante** | **Experiência Técnica** | **Experiência com Análise** | **Idade** |
|------------------|-------------------------|------------------------------|-----------|
| **P1** | Intermediária | Básica | 22-28 anos |
| **P2** | Básica | Avançada | 35-45 anos |
| **P3** | Avançada | Básica | 20-25 anos |
| **P4** | Básica | Intermediária | 28-35 anos |
| **P5** | Avançada | Avançada | 30-40 anos |

### Cenários de Teste de Usabilidade

| **Cenário** | **Métrica de Avaliação** | **Resultado Esperado** | **Observações** |
|-------------|--------------------------|------------------------|-----------------|
| **TU01 - Primeiro Acesso ao Sistema** | Tempo para localizar funcionalidade principal; Taxa de sucesso; SUS Score | Usuário identifica área de upload em < 30 segundos; 100% conseguem fazer primeiro upload; SUS > 70 | Observar hesitações e pontos de confusão na navegação inicial |
| **TU02 - Upload e Análise de Ligação** | Tempo total da tarefa; Número de erros; Satisfação subjetiva | Tarefa completa em < 5 minutos; Máximo 1 erro por usuário; Satisfação ≥ 4/5 | Documentar dificuldades no processo de upload e interpretação de resultados |
| **TU03 - Interpretação de Score e Evidências** | Precisão na interpretação; Tempo para identificar problemas; Confiança na análise | 90% interpretam score corretamente; Identificam problema principal em < 2 minutos; Confiança ≥ 4/5 | Avaliar clareza das evidências textuais e justificativas do score |
| **TU04 - Uso de Filtros e Dashboard** | Eficiência no uso de filtros e compreensão dos gráficos | Aplicam filtros corretos em < 1 minuto; Interpretam gráficos sem ajuda| Observar intuitividade dos controles e clareza das visualizações |
| **TU05 - Exportação de Relatórios** | Facilidade para encontrar função; Qualidade do relatório exportado; Utilidade percebida | Localizam exportação em < 45 segundos; Relatório contém informações necessárias; Utilidade ≥ 4/5 | Verificar se formato atende necessidades de apresentação e análise |
| **TU06 - Recuperação de Erros** | Capacidade de identificar erro; Facilidade de correção; Clareza das mensagens | Identificam erro em < 1 minuto; Corrigem sem ajuda externa; Mensagens compreensíveis para 100% | Testar cenários como upload de arquivo inválido e falhas de conexão |
| **TU07 - Navegação Geral e Fluxo** | Fluidez da navegação; Consistência da interface; SUS Score geral | Navegação sem hesitação; Interface consistente em todas as telas; SUS > 75 | Avaliação holística da experiência completa do usuário |

### Metodologia de Avaliação - SUS (System Usability Scale)

&emsp;O questionário SUS será aplicado após cada sessão de teste, contendo 10 questões padronizadas:

1. Eu acho que gostaria de usar esse sistema com frequência
2. Eu acho o sistema desnecessariamente complexo
3. Eu acho que o sistema foi fácil de usar
4. Eu acho que precisaria do apoio de um suporte técnico para conseguir usar o sistema
5. Eu acho que as várias funções do sistema foram bem integradas
6. Eu acho que houve muita inconsistência no sistema
7. Eu imagino que a maioria das pessoas aprenderia a usar esse sistema rapidamente
8. Eu acho o sistema muito pesado para usar
9. Eu me senti muito confiante usando o sistema
10. Eu precisei aprender várias coisas antes que eu pudesse continuar usando o sistema

**Escala de Resposta:** 1 (Discordo totalmente) a 5 (Concordo totalmente)
**Meta de Score SUS:** ≥ 75 pontos (considerado "Bom" a "Excelente")

### Execução e Análise dos Resultados

**Protocolo de Execução:**
1. **Briefing inicial** (5 min): Explicação do contexto e objetivos sem revelar detalhes específicos
2. **Execução de tarefas** (20-30 min): Participante realiza os testes enquanto pesquisadores observam silenciosamente
3. **Questionário SUS** (5 min): Preenchimento do formulário padronizado
4. **Entrevista pós-teste** (10 min): Coleta de feedback qualitativo e sugestões

**Análise Quantitativa:**
- Cálculo do Score SUS médio e por participante
- Análise de tempos de execução por tarefa
- Taxa de sucesso e número de erros por cenário


**Análise Qualitativa:**
- Identificação de padrões de dificuldade
- Categorização de feedback e sugestões
- Priorização de melhorias baseada em impacto e frequência


&emsp;Os resultados dos testes de usabilidade serão muito importantes para validar a adequação da interface às necessidades dos usuários e orientar possíveis ajustes antes da implementação final.

## 9.3.2 Resultados

_conteúdo_

# 10. Procedimentos de Implantação

_conteúdo_

## 10.1 Implantação e Configuração do Banco de Dados

_conteúdo_

## 10.2 Implantação do Protótipo para uso por equipe de desenvolvimento

_conteúdo_

## 10.3 Processo de Deploy do Algoritmo em Nuvem Comercial 

&emsp; O Amazon SageMaker é um serviço totalmente gerenciado da AWS que permite construir, treinar e implantar modelos de machine learning em escala. Ele foi escolhido neste projeto de NLP (Processamento de Linguagem Natural) porque:

- Possui suporte nativo para treinamento e deploy de modelos;
- Oferece SageMaker Studio e Notebooks para desenvolvimento colaborativo;
- Integra-se com outros serviços da AWS (S3, Lambda, DynamoDB), facilitando a comunicação entre camadas;
- Permite configuração escalável e segura, com controle granular de permissões via IAM.

&emsp; Dessa forma, o SageMaker centraliza todo o ciclo de vida do modelo (treinamento → deploy → monitoramento), reduzindo a complexidade operacional e garantindo maior governança sobre os experimentos.

### **1. Cadastro na AWS**

&emsp;Para utilização do Amazon SageMaker, é necessário possuir uma conta válida na AWS com acesso habilitado ao serviço. No contexto deste projeto, consideramos o ambiente disponibilizado pelo AWS Academy, que já oferece permissões pré-configuradas para estudantes.

<div align="center">
<sub>Figura X - Tela inicial do SageMaker</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/sage-maker.png" alt='Tela inicial do SageMaker' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### **2. Configuração do Ambiente**
#### **Passo 1 – Criação da Role**

Na criação da role, devem ser definidos:

- **Nome da role:** Deve ser descritivo e associado ao projeto (ex.: Role-SageMaker-NLP).
- **Descrição:** Breve texto indicando a finalidade (ex.: "Permite acesso do SageMaker a DynamoDB e S3 para NLP").
- **Persona:** A persona Cientista de Dados é a mais adequada, pois já disponibiliza permissões padrão necessárias para execução de notebooks e jobs de ML.

<div align="center">
<sub>Figura X - Criação de role no console IAM</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/criar-role.png" alt='Criação de role no console IAM' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

Ainda no passo 1, se houver uma etapa de seleção inicial de permissões/atividades, registre-as e avance.

<div align="center">
<sub>Figura X - Continuação da criação da role.</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/continuar-role.png" alt='Continuação da criação da role.' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

#### **Passo 2 — Definição de Permissões (Atividades de ML)**

Nesta etapa, habilite apenas as atividades de ML necessárias ao projeto. Para este caso de uso:

- **Gerenciar trabalhos de ML:** Habilitar (treinamento e processamento).
- **Gerenciar modelos:** Habilitar (registro de modelos e criação de endpoints de inferência).
- **Gerenciar tabelas do Glue (Data Catalog):** Opcional; habilitar apenas se o pipeline consultar metadados por meio do Glue.
- **Canvas Kendra Access:** Não habilitar, a menos que o time use o Canvas com Kendra.
- **Executar aplicativos sem servidor do Studio EMR:** Não habilitar para este projeto (não há processamento no EMR).

<div align="center">
<sub>Figura X - Ativar atividades de ML (parte 1).</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/atividade-ML.png" alt='Ativar atividades de ML (parte 1).' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

<div align="center">
<sub>Figura X - Ativar atividades de ML (parte 2).</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/atividade-ML2.png" alt='Ativar atividades de ML (parte 2).' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

Ainda no passo 2, alguns campos aparecem para configuração:

- **Baldes S3 (Buckets S3):** aqui devem ser informados os buckets que o SageMaker poderá acessar. Isso garante que o ambiente terá permissão para ler dados de entrada (como datasets) e salvar resultados (como modelos treinados).
  - Preencher com o nome do bucket criado para o projeto, ex.: meu-bucket.
  - Caso haja mais de um bucket, utilize o botão Adicionar.

- **Acesso ao bucket S3:** campo semelhante, mas voltado para acesso direto do ambiente. Deve ser preenchido com o mesmo bucket do projeto ou outro que armazene os dados necessários.
  - Pode ser digitado o nome do bucket ou selecionado por meio do botão Navegar S3.

&emsp; Esses campos precisam ser preenchidos porque, sem eles, o SageMaker não terá autorização para manipular os dados do projeto. Sempre que possível, utilize apenas os buckets realmente necessários, evitando concessões amplas.

#### **Passo 3 — Políticas adicionais e Tags**

Além das atividades de ML, a role pode receber políticas do IAM para integração com serviços utilizados no projeto:

- AmazonS3FullAccess ou (preferencialmente) política customizada restrita aos buckets/prefixos definidos acima.
- AmazonDynamoDBFullAccess ou política customizada restrita às tabelas do projeto (armazenar resultados/metadata).
- CloudWatchFullAccess para logs e métricas (observabilidade).

&emsp; Utilize tags para governança e rastreabilidade (custos, responsáveis, ambiente): Projeto=ComgasNLP, Ambiente=Protótipo, Equipe=EngenhariaDeSoftware.

<div align="center">
<sub>Figura X - Politicas e tags</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/politicas-tags.png" alt='Politicas e tags' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

#### **Passo 4 — Revisão e Associação**

&emsp; Revise o escopo de permissões e confirme a criação. Depois, associe a role ao SageMaker Studio (ou ao job/notebook que irá executá-la). A partir daí, notebooks e jobs poderão:

- Ler ou gravar datasets e artefatos nos buckets S3 informados;
- Persistir resultados no DynamoDB (se configurado);
- Enviar logs e métricas para CloudWatch.

<div align="center">
<sub>Figura X - Função de revisão</sub><br>
<br>
<img src="./img/deploy-do-algoritmo/funcao-revisao.png" alt='Função de revisão' width="80%">
<br>
<br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>
<br>

### **Conclusão: Próximos Passos após o Deploy**

&emsp; Os fine-tunes dos modelos podem ser usados localmente, mas, ao seguir este tutorial, o modelo ficará deployado na nuvem AWS, aproveitando a escalabilidade e os serviços integrados.

&emsp; Após clicar em "Submit", a AWS cria sua função de serviço do IAM com as permissões e configurações definidas. Uma vez que a função esteja pronta, ela estará disponível para ser associada ao Amazon SageMaker Studio, a um notebook ou a um job de treinamento.

A partir deste ponto, o ambiente de desenvolvimento e o modelo de machine learning terão as permissões necessárias para:

- **Acessar os dados de entrada:** Ter acesso aos áudios no s3
- **Persistir os resultados:** Salvar os resultados das análises, como as notas de qualidade e os metadados das chamadas, no Amazon DynamoDB.
- **Monitorar a execução:** Enviar logs e métricas de desempenho para o Amazon CloudWatch para garantir a observabilidade do sistema.

&emsp; Com a função de serviço criada e configurada, o próximo passo no processo de deploy é iniciar o treinamento e a implantação do modelo de NLP. As etapas a seguir incluem:

- **Treinamento do Modelo:** Utilizar o SageMaker para executar o código de treinamento do algoritmo de NLP, consumindo os dados do S3. 
- **Criação do Endpoint:** Após o treinamento, o modelo será implantado em um endpoint de inferência no Amazon SageMaker. Esse endpoint não é acessado diretamente pela aplicação final, mas sim consumido por funções AWS Lambda, que atuam como camadas de API.

&emsp; Essa arquitetura garante que todo o ciclo de vida do modelo seja centralizado no SageMaker, simplificando o processo de treinamento, implantação e monitoramento.

# Referências

_conteúdo_
